"""
Forms for user authentication and profile management
"""

from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta
from .models import CustomUser, UserProfile, VerificationCode
from .utils import check_password_strength, validate_phone_number, create_verification_code
from core.validators import InputSanitizer, InputValidator


class CustomUserCreationForm(UserCreationForm):
    """
    Form for creating new users with phone number
    """
    phone_number = forms.CharField(
        max_length=17,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+************'
        }),
        help_text="Enter phone number in international format"
    )
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )
    first_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'First Name'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Last Name'
        })
    )
    date_of_birth = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        help_text="You must be 18 years or older to register"
    )
    
    class Meta:
        model = CustomUser
        fields = (
            'phone_number', 'email', 'first_name', 'last_name', 
            'date_of_birth', 'password1', 'password2'
        )
    
    def clean_phone_number(self):
        phone_number = self.cleaned_data.get('phone_number')

        # Sanitize phone number
        sanitized_phone = InputSanitizer.sanitize_phone_number(phone_number)

        # Validate phone number format
        if not InputValidator.validate_phone_number(sanitized_phone):
            raise ValidationError("Please enter a valid phone number in international format.")

        # Check for existing user
        if CustomUser.objects.filter(phone_number=sanitized_phone).exists():
            raise ValidationError("A user with this phone number already exists.")

        return sanitized_phone
    
    def clean_date_of_birth(self):
        date_of_birth = self.cleaned_data.get('date_of_birth')
        if date_of_birth:
            today = timezone.now().date()
            age = today.year - date_of_birth.year
            if today.month < date_of_birth.month or \
               (today.month == date_of_birth.month and today.day < date_of_birth.day):
                age -= 1
            if age < 18:
                raise ValidationError("You must be 18 years or older to register.")
        return date_of_birth

    def clean_password1(self):
        password1 = self.cleaned_data.get('password1')

        # Use enhanced password strength validation
        is_strong, errors = InputValidator.validate_password_strength(password1)
        if not is_strong:
            raise ValidationError(errors)

        return password1

    def clean_email(self):
        email = self.cleaned_data.get('email')

        # Sanitize email
        sanitized_email = InputSanitizer.sanitize_text(email).lower()

        # Validate email format
        if not InputValidator.validate_email_address(sanitized_email):
            raise ValidationError("Please enter a valid email address.")

        return sanitized_email

    def clean_first_name(self):
        first_name = self.cleaned_data.get('first_name')
        return InputSanitizer.sanitize_text(first_name)

    def clean_last_name(self):
        last_name = self.cleaned_data.get('last_name')
        return InputSanitizer.sanitize_text(last_name)


class CustomAuthenticationForm(AuthenticationForm):
    """
    Custom authentication form using phone number
    """
    username = forms.CharField(
        label="Phone Number",
        max_length=17,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+************',
            'autofocus': True
        })
    )
    password = forms.CharField(
        label="Password",
        strip=False,
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Password'
        })
    )

    def clean_username(self):
        username = self.cleaned_data.get('username')

        # Sanitize phone number
        sanitized_phone = InputSanitizer.sanitize_phone_number(username)

        # Validate phone number format
        if not InputValidator.validate_phone_number(sanitized_phone):
            raise ValidationError("Please enter a valid phone number.")

        return sanitized_phone


class VerificationCodeForm(forms.Form):
    """
    Form for entering SMS verification codes
    """
    code = forms.CharField(
        max_length=6,
        min_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control text-center',
            'placeholder': '000000',
            'maxlength': '6'
        }),
        help_text="Enter the 6-digit code sent to your phone"
    )
    
    def __init__(self, user=None, code_type='registration', *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = user
        self.code_type = code_type
    
    def clean_code(self):
        code = self.cleaned_data.get('code')
        if self.user and code:
            try:
                verification_code = VerificationCode.objects.get(
                    user=self.user,
                    code=code,
                    code_type=self.code_type,
                    is_used=False
                )
                if verification_code.is_expired:
                    raise ValidationError("This verification code has expired.")
            except VerificationCode.DoesNotExist:
                raise ValidationError("Invalid verification code.")
        return code


class UserProfileForm(forms.ModelForm):
    """
    Form for updating user profile information
    """
    class Meta:
        model = UserProfile
        fields = [
            'preferred_language', 'county', 'city', 'address',
            'favorite_sports', 'notification_preferences'
        ]
        widgets = {
            'preferred_language': forms.Select(attrs={'class': 'form-control'}),
            'county': forms.TextInput(attrs={'class': 'form-control'}),
            'city': forms.TextInput(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }


class PasswordResetRequestForm(forms.Form):
    """
    Form for requesting password reset via SMS
    """
    phone_number = forms.CharField(
        max_length=17,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+************'
        }),
        help_text="Enter your registered phone number"
    )
    
    def clean_phone_number(self):
        phone_number = self.cleaned_data.get('phone_number')
        try:
            user = CustomUser.objects.get(phone_number=phone_number)
            self.user = user
        except CustomUser.DoesNotExist:
            raise ValidationError("No account found with this phone number.")
        return phone_number


