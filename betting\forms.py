"""
Forms for betting functionality with enhanced validation
"""

from django import forms
from django.core.exceptions import ValidationError
from decimal import Decimal
from .models import Bet, BetSelection
from core.validators import InputSanitizer, InputValidator, validate_form_data


class BetPlacementForm(forms.Form):
    """
    Form for placing bets with enhanced validation
    """
    stake = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=Decimal('0.01'),
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter stake amount',
            'step': '0.01',
            'min': '0.01'
        })
    )
    
    bet_type = forms.ChoiceField(
        choices=[
            ('single', 'Single Bet'),
            ('multi', 'Multi Bet'),
            ('system', 'System Bet')
        ],
        widget=forms.Select(attrs={
            'class': 'form-control'
        })
    )
    
    selections = forms.CharField(
        widget=forms.HiddenInput(),
        required=True
    )
    
    def clean_stake(self):
        stake = self.cleaned_data.get('stake')
        
        # Use enhanced validation
        is_valid, error_msg = InputValidator.validate_betting_amount(stake)
        if not is_valid:
            raise ValidationError(error_msg)
        
        return InputSanitizer.sanitize_decimal(stake)
    
    def clean_selections(self):
        selections = self.cleaned_data.get('selections')
        
        if not selections:
            raise ValidationError("At least one selection is required")
        
        try:
            import json
            selections_data = json.loads(selections)
            
            if not isinstance(selections_data, list) or len(selections_data) == 0:
                raise ValidationError("Invalid selections format")
            
            # Validate each selection
            for selection in selections_data:
                if not isinstance(selection, dict):
                    raise ValidationError("Invalid selection format")
                
                required_fields = ['market_id', 'selection', 'odds']
                for field in required_fields:
                    if field not in selection:
                        raise ValidationError(f"Missing required field: {field}")
                
                # Validate odds
                try:
                    odds = Decimal(str(selection['odds']))
                    if odds <= 1:
                        raise ValidationError("Invalid odds value")
                except (ValueError, TypeError):
                    raise ValidationError("Invalid odds format")
            
            return selections_data
            
        except json.JSONDecodeError:
            raise ValidationError("Invalid selections format")


class BetCodeForm(forms.Form):
    """
    Form for loading bet slip from code
    """
    bet_code = forms.CharField(
        max_length=12,
        min_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter bet code',
            'style': 'text-transform: uppercase;'
        })
    )
    
    def clean_bet_code(self):
        bet_code = self.cleaned_data.get('bet_code')
        
        # Sanitize input
        sanitized_code = InputSanitizer.sanitize_text(bet_code).upper()
        
        # Validate format
        if not InputValidator.validate_bet_code(sanitized_code):
            raise ValidationError("Invalid bet code format")
        
        return sanitized_code


class BetSearchForm(forms.Form):
    """
    Form for searching bets
    """
    search_query = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search by ticket number or team name'
        })
    )
    
    status = forms.ChoiceField(
        choices=[
            ('', 'All Statuses'),
            ('pending', 'Pending'),
            ('won', 'Won'),
            ('lost', 'Lost'),
            ('void', 'Void')
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        })
    )
    
    bet_type = forms.ChoiceField(
        choices=[
            ('', 'All Types'),
            ('single', 'Single'),
            ('multi', 'Multi'),
            ('system', 'System')
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-control'
        })
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    def clean_search_query(self):
        query = self.cleaned_data.get('search_query')
        if query:
            return InputSanitizer.sanitize_text(query)
        return query
    
    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise ValidationError("Start date cannot be after end date")
        
        return cleaned_data


class StakeCalculatorForm(forms.Form):
    """
    Form for calculating potential winnings
    """
    stake = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=Decimal('0.01'),
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter stake',
            'step': '0.01'
        })
    )
    
    total_odds = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=Decimal('1.01'),
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Total odds',
            'step': '0.01',
            'readonly': True
        })
    )
    
    def clean_stake(self):
        stake = self.cleaned_data.get('stake')
        is_valid, error_msg = InputValidator.validate_betting_amount(stake)
        if not is_valid:
            raise ValidationError(error_msg)
        return InputSanitizer.sanitize_decimal(stake)
    
    def clean_total_odds(self):
        odds = self.cleaned_data.get('total_odds')
        sanitized_odds = InputSanitizer.sanitize_decimal(odds)
        
        if sanitized_odds and sanitized_odds <= 1:
            raise ValidationError("Odds must be greater than 1.00")
        
        return sanitized_odds


def validate_bet_placement_data(data):
    """
    Validate bet placement data using the validation framework
    """
    validation_rules = {
        'stake': {
            'required': True,
            'type': 'decimal',
            'min_value': 10.0,
            'max_value': 100000.0
        },
        'bet_type': {
            'required': True,
            'pattern': r'^(single|multi|system)$',
            'pattern_error': 'Invalid bet type'
        },
        'selections': {
            'required': True,
            'min_length': 1
        }
    }
    
    return validate_form_data(data, validation_rules)


def validate_bet_search_data(data):
    """
    Validate bet search data
    """
    validation_rules = {
        'search_query': {
            'required': False,
            'max_length': 100
        },
        'status': {
            'required': False,
            'pattern': r'^(pending|won|lost|void)?$',
            'pattern_error': 'Invalid status'
        },
        'bet_type': {
            'required': False,
            'pattern': r'^(single|multi|system)?$',
            'pattern_error': 'Invalid bet type'
        }
    }
    
    return validate_form_data(data, validation_rules)
