"""
Analytics views for dashboard and reporting
"""

from django.shortcuts import render, get_object_or_404
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.db.models import Sum, Count, Avg, Q
from django.core.paginator import Paginator
from datetime import datetime, timedelta
import json

from .models import (
    UserActivity, BettingPattern, DailyMetrics, 
    SportPopularity, UserSegment, RevenueReport
)
from .utils import (
    get_platform_analytics_summary, get_user_analytics_summary,
    calculate_daily_metrics, generate_revenue_report, export_analytics_data
)
from .signals import track_page_view


@staff_member_required
def analytics_dashboard(request):
    """Main analytics dashboard"""
    track_page_view(request, 'analytics_dashboard')
    
    # Get date range from request
    days = int(request.GET.get('days', 30))
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)
    
    # Get platform summary
    platform_summary = get_platform_analytics_summary(days)
    
    # Get recent daily metrics
    recent_metrics = DailyMetrics.objects.filter(
        date__gte=start_date.date()
    ).order_by('-date')[:7]
    
    # Get top sports
    top_sports = SportPopularity.objects.filter(
        date__gte=start_date.date()
    ).values('sport').annotate(
        total_bets=Sum('total_bets'),
        total_amount=Sum('total_amount_bet')
    ).order_by('-total_bets')[:5]
    
    # Get user segment distribution
    segments = UserSegment.objects.filter(
        is_active=True
    ).values('segment_type').annotate(
        count=Count('id')
    ).order_by('-count')
    
    context = {
        'platform_summary': platform_summary,
        'recent_metrics': recent_metrics,
        'top_sports': top_sports,
        'segments': segments,
        'days': days,
        'date_range': {
            'start': start_date.date(),
            'end': end_date.date()
        }
    }
    
    return render(request, 'analytics/dashboard.html', context)


@staff_member_required
def user_analytics(request, user_id=None):
    """User-specific analytics"""
    track_page_view(request, 'user_analytics')
    
    if user_id:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        user = get_object_or_404(User, id=user_id)
        
        days = int(request.GET.get('days', 30))
        user_summary = get_user_analytics_summary(user, days)
        
        context = {
            'user': user,
            'user_summary': user_summary,
            'days': days
        }
        
        return render(request, 'analytics/user_analytics.html', context)
    
    # List all users with analytics
    from django.contrib.auth import get_user_model
    User = get_user_model()
    
    users = User.objects.annotate(
        activity_count=Count('activities'),
        bet_count=Count('activities', filter=Q(activities__action_type='bet_placed'))
    ).order_by('-activity_count')
    
    paginator = Paginator(users, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'users': page_obj
    }
    
    return render(request, 'analytics/user_list.html', context)


@staff_member_required
def betting_analytics(request):
    """Betting-specific analytics"""
    track_page_view(request, 'betting_analytics')
    
    days = int(request.GET.get('days', 30))
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)
    
    # Betting patterns by sport
    sport_patterns = BettingPattern.objects.values('sport').annotate(
        total_bets=Sum('total_bets'),
        total_amount=Sum('total_amount_bet'),
        avg_win_rate=Avg('win_rate'),
        unique_users=Count('user', distinct=True)
    ).order_by('-total_amount')
    
    # Daily betting trends
    daily_bets = DailyMetrics.objects.filter(
        date__gte=start_date.date()
    ).order_by('date').values('date', 'total_bets_placed', 'total_bet_amount')
    
    # Top betting users
    from django.contrib.auth import get_user_model
    User = get_user_model()
    
    top_bettors = User.objects.annotate(
        total_bet_amount=Sum('betting_patterns__total_amount_bet'),
        total_bets=Sum('betting_patterns__total_bets'),
        avg_win_rate=Avg('betting_patterns__win_rate')
    ).filter(total_bet_amount__isnull=False).order_by('-total_bet_amount')[:10]
    
    context = {
        'sport_patterns': sport_patterns,
        'daily_bets': list(daily_bets),
        'top_bettors': top_bettors,
        'days': days
    }
    
    return render(request, 'analytics/betting_analytics.html', context)


@staff_member_required
def financial_reports(request):
    """Financial reporting dashboard"""
    track_page_view(request, 'financial_reports')
    
    report_type = request.GET.get('type', 'monthly')
    
    # Get recent revenue reports
    reports = RevenueReport.objects.filter(
        report_type=report_type
    ).order_by('-period_start')[:12]
    
    # Generate current period report if needed
    current_report = generate_revenue_report(report_type)
    
    # Calculate financial metrics
    total_revenue = sum(report.gross_gaming_revenue for report in reports)
    avg_profit_margin = sum(report.profit_margin for report in reports) / len(reports) if reports else 0
    
    context = {
        'reports': reports,
        'current_report': current_report,
        'report_type': report_type,
        'total_revenue': total_revenue,
        'avg_profit_margin': avg_profit_margin
    }
    
    return render(request, 'analytics/financial_reports.html', context)


@staff_member_required
def api_analytics_data(request):
    """API endpoint for analytics data (for charts)"""
    data_type = request.GET.get('type', 'daily_metrics')
    days = int(request.GET.get('days', 30))
    
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)
    
    if data_type == 'daily_metrics':
        metrics = DailyMetrics.objects.filter(
            date__gte=start_date.date()
        ).order_by('date').values(
            'date', 'active_users', 'total_bets_placed', 
            'total_bet_amount', 'new_registrations'
        )
        
        data = {
            'labels': [str(m['date']) for m in metrics],
            'datasets': [
                {
                    'label': 'Active Users',
                    'data': [m['active_users'] for m in metrics],
                    'borderColor': 'rgb(75, 192, 192)',
                    'tension': 0.1
                },
                {
                    'label': 'Total Bets',
                    'data': [m['total_bets_placed'] for m in metrics],
                    'borderColor': 'rgb(255, 99, 132)',
                    'tension': 0.1
                }
            ]
        }
    
    elif data_type == 'sport_popularity':
        sports = SportPopularity.objects.filter(
            date__gte=start_date.date()
        ).values('sport').annotate(
            total_bets=Sum('total_bets'),
            total_amount=Sum('total_amount_bet')
        ).order_by('-total_bets')[:10]
        
        data = {
            'labels': [s['sport'] for s in sports],
            'datasets': [{
                'label': 'Total Bets',
                'data': [s['total_bets'] for s in sports],
                'backgroundColor': [
                    'rgba(255, 99, 132, 0.2)',
                    'rgba(54, 162, 235, 0.2)',
                    'rgba(255, 205, 86, 0.2)',
                    'rgba(75, 192, 192, 0.2)',
                    'rgba(153, 102, 255, 0.2)',
                ]
            }]
        }
    
    elif data_type == 'user_segments':
        segments = UserSegment.objects.filter(
            is_active=True
        ).values('segment_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        data = {
            'labels': [s['segment_type'].replace('_', ' ').title() for s in segments],
            'datasets': [{
                'data': [s['count'] for s in segments],
                'backgroundColor': [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        }
    
    else:
        data = {'error': 'Invalid data type'}
    
    return JsonResponse(data)


@staff_member_required
def export_data(request):
    """Export analytics data"""
    data_type = request.GET.get('type', 'user_activities')
    format_type = request.GET.get('format', 'json')
    days = int(request.GET.get('days', 30))
    
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)
    
    exported_data = export_analytics_data(data_type, start_date, end_date, format_type)
    
    if format_type == 'json':
        response = HttpResponse(exported_data, content_type='application/json')
        response['Content-Disposition'] = f'attachment; filename="{data_type}_{start_date.date()}_to_{end_date.date()}.json"'
    else:
        response = HttpResponse(exported_data, content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{data_type}_{start_date.date()}_to_{end_date.date()}.csv"'
    
    return response


@login_required
def user_personal_analytics(request):
    """Personal analytics for logged-in users"""
    track_page_view(request, 'personal_analytics')
    
    days = int(request.GET.get('days', 30))
    user_summary = get_user_analytics_summary(request.user, days)
    
    # Get user's betting patterns
    betting_patterns = BettingPattern.objects.filter(user=request.user)
    
    # Get recent activities
    recent_activities = UserActivity.objects.filter(
        user=request.user
    ).order_by('-timestamp')[:20]
    
    context = {
        'user_summary': user_summary,
        'betting_patterns': betting_patterns,
        'recent_activities': recent_activities,
        'days': days
    }
    
    return render(request, 'analytics/personal_analytics.html', context)
