"""
Analytics views for dashboard and reporting
"""

from django.shortcuts import render, get_object_or_404
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.db.models import Sum, Count, Avg, Q
from django.core.paginator import Paginator
from datetime import datetime, timedelta
import json

from .models import (
    UserActivity, BettingPattern, DailyMetrics, 
    SportPopularity, UserSegment, RevenueReport
)
from .utils import (
    get_platform_analytics_summary, get_user_analytics_summary,
    calculate_daily_metrics, generate_revenue_report, export_analytics_data
)
from .signals import track_page_view
from .exporters import AnalyticsExporter, get_available_export_formats
from .financial_reports import TransactionReporter, generate_transaction_report
from .profit_loss import ProfitLossCalculator, generate_profit_loss_report


@staff_member_required
def analytics_dashboard(request):
    """Main analytics dashboard"""
    track_page_view(request, 'analytics_dashboard')
    
    # Get date range from request
    days = int(request.GET.get('days', 30))
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)
    
    # Get platform summary
    platform_summary = get_platform_analytics_summary(days)
    
    # Get recent daily metrics
    recent_metrics = DailyMetrics.objects.filter(
        date__gte=start_date.date()
    ).order_by('-date')[:7]
    
    # Get top sports
    top_sports = SportPopularity.objects.filter(
        date__gte=start_date.date()
    ).values('sport').annotate(
        total_bets=Sum('total_bets'),
        total_amount=Sum('total_amount_bet')
    ).order_by('-total_bets')[:5]
    
    # Get user segment distribution
    segments = UserSegment.objects.filter(
        is_active=True
    ).values('segment_type').annotate(
        count=Count('id')
    ).order_by('-count')
    
    context = {
        'platform_summary': platform_summary,
        'recent_metrics': recent_metrics,
        'top_sports': top_sports,
        'segments': segments,
        'days': days,
        'date_range': {
            'start': start_date.date(),
            'end': end_date.date()
        }
    }
    
    return render(request, 'analytics/dashboard.html', context)


@staff_member_required
def user_analytics(request, user_id=None):
    """User-specific analytics"""
    track_page_view(request, 'user_analytics')
    
    if user_id:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        user = get_object_or_404(User, id=user_id)
        
        days = int(request.GET.get('days', 30))
        user_summary = get_user_analytics_summary(user, days)
        
        context = {
            'user': user,
            'user_summary': user_summary,
            'days': days
        }
        
        return render(request, 'analytics/user_analytics.html', context)
    
    # List all users with analytics
    from django.contrib.auth import get_user_model
    User = get_user_model()
    
    users = User.objects.annotate(
        activity_count=Count('activities'),
        bet_count=Count('activities', filter=Q(activities__action_type='bet_placed'))
    ).order_by('-activity_count')
    
    paginator = Paginator(users, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'users': page_obj
    }
    
    return render(request, 'analytics/user_list.html', context)


@staff_member_required
def betting_analytics(request):
    """Betting-specific analytics"""
    track_page_view(request, 'betting_analytics')
    
    days = int(request.GET.get('days', 30))
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)
    
    # Betting patterns by sport
    sport_patterns = BettingPattern.objects.values('sport').annotate(
        total_bets=Sum('total_bets'),
        total_amount=Sum('total_amount_bet'),
        avg_win_rate=Avg('win_rate'),
        unique_users=Count('user', distinct=True)
    ).order_by('-total_amount')
    
    # Daily betting trends
    daily_bets = DailyMetrics.objects.filter(
        date__gte=start_date.date()
    ).order_by('date').values('date', 'total_bets_placed', 'total_bet_amount')
    
    # Top betting users
    from django.contrib.auth import get_user_model
    User = get_user_model()
    
    top_bettors = User.objects.annotate(
        total_bet_amount=Sum('betting_patterns__total_amount_bet'),
        total_bets=Sum('betting_patterns__total_bets'),
        avg_win_rate=Avg('betting_patterns__win_rate')
    ).filter(total_bet_amount__isnull=False).order_by('-total_bet_amount')[:10]
    
    context = {
        'sport_patterns': sport_patterns,
        'daily_bets': list(daily_bets),
        'top_bettors': top_bettors,
        'days': days
    }
    
    return render(request, 'analytics/betting_analytics.html', context)


@staff_member_required
def financial_reports(request):
    """Enhanced financial reporting dashboard"""
    track_page_view(request, 'financial_reports')

    report_type = request.GET.get('type', 'monthly')
    days = int(request.GET.get('days', 30))

    # Get date range
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)

    # Generate comprehensive transaction report
    transaction_reporter = TransactionReporter(start_date, end_date)
    transaction_summary = transaction_reporter.get_transaction_summary()
    daily_breakdown = transaction_reporter.get_daily_transaction_breakdown()
    top_users = transaction_reporter.get_user_transaction_analysis(20)
    trends = transaction_reporter.get_transaction_trends()

    # Get recent revenue reports
    reports = RevenueReport.objects.filter(
        report_type=report_type
    ).order_by('-period_start')[:12]

    # Generate current period report if needed
    current_report = generate_revenue_report(report_type)

    # Calculate financial metrics
    total_revenue = sum(report.gross_gaming_revenue for report in reports)
    avg_profit_margin = sum(report.profit_margin for report in reports) / len(reports) if reports else 0

    context = {
        'reports': reports,
        'current_report': current_report,
        'report_type': report_type,
        'total_revenue': total_revenue,
        'avg_profit_margin': avg_profit_margin,
        'transaction_summary': transaction_summary,
        'daily_breakdown': daily_breakdown,
        'top_users': top_users,
        'trends': trends,
        'days': days
    }

    return render(request, 'analytics/financial_reports.html', context)


@staff_member_required
def api_analytics_data(request):
    """API endpoint for analytics data (for charts)"""
    data_type = request.GET.get('type', 'daily_metrics')
    days = int(request.GET.get('days', 30))
    
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)
    
    if data_type == 'daily_metrics':
        metrics = DailyMetrics.objects.filter(
            date__gte=start_date.date()
        ).order_by('date').values(
            'date', 'active_users', 'total_bets_placed', 
            'total_bet_amount', 'new_registrations'
        )
        
        data = {
            'labels': [str(m['date']) for m in metrics],
            'datasets': [
                {
                    'label': 'Active Users',
                    'data': [m['active_users'] for m in metrics],
                    'borderColor': 'rgb(75, 192, 192)',
                    'tension': 0.1
                },
                {
                    'label': 'Total Bets',
                    'data': [m['total_bets_placed'] for m in metrics],
                    'borderColor': 'rgb(255, 99, 132)',
                    'tension': 0.1
                }
            ]
        }
    
    elif data_type == 'sport_popularity':
        sports = SportPopularity.objects.filter(
            date__gte=start_date.date()
        ).values('sport').annotate(
            total_bets=Sum('total_bets'),
            total_amount=Sum('total_amount_bet')
        ).order_by('-total_bets')[:10]
        
        data = {
            'labels': [s['sport'] for s in sports],
            'datasets': [{
                'label': 'Total Bets',
                'data': [s['total_bets'] for s in sports],
                'backgroundColor': [
                    'rgba(255, 99, 132, 0.2)',
                    'rgba(54, 162, 235, 0.2)',
                    'rgba(255, 205, 86, 0.2)',
                    'rgba(75, 192, 192, 0.2)',
                    'rgba(153, 102, 255, 0.2)',
                ]
            }]
        }
    
    elif data_type == 'user_segments':
        segments = UserSegment.objects.filter(
            is_active=True
        ).values('segment_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        data = {
            'labels': [s['segment_type'].replace('_', ' ').title() for s in segments],
            'datasets': [{
                'data': [s['count'] for s in segments],
                'backgroundColor': [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        }
    
    else:
        data = {'error': 'Invalid data type'}
    
    return JsonResponse(data)


@staff_member_required
def export_data(request):
    """Export analytics data using enhanced export system"""
    data_type = request.GET.get('type', 'user_activities')
    format_type = request.GET.get('format', 'json')
    days = int(request.GET.get('days', 30))
    user_id = request.GET.get('user_id')
    sport = request.GET.get('sport')

    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)

    try:
        if data_type == 'user_activities':
            return AnalyticsExporter.export_user_activities(
                start_date, end_date, format_type, user_id
            )
        elif data_type == 'betting_patterns':
            return AnalyticsExporter.export_betting_patterns(format_type, sport)
        elif data_type == 'daily_metrics':
            return AnalyticsExporter.export_daily_metrics(start_date, end_date, format_type)
        elif data_type == 'revenue_reports':
            report_type = request.GET.get('report_type', 'monthly')
            return AnalyticsExporter.export_revenue_reports(report_type, format_type)
        elif data_type == 'sport_popularity':
            return AnalyticsExporter.export_sport_popularity(start_date, end_date, format_type)
        else:
            return JsonResponse({'error': 'Invalid data type'}, status=400)

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def user_personal_analytics(request):
    """Personal analytics for logged-in users"""
    track_page_view(request, 'personal_analytics')
    
    days = int(request.GET.get('days', 30))
    user_summary = get_user_analytics_summary(request.user, days)
    
    # Get user's betting patterns
    betting_patterns = BettingPattern.objects.filter(user=request.user)
    
    # Get recent activities
    recent_activities = UserActivity.objects.filter(
        user=request.user
    ).order_by('-timestamp')[:20]
    
    context = {
        'user_summary': user_summary,
        'betting_patterns': betting_patterns,
        'recent_activities': recent_activities,
        'days': days
    }
    
    return render(request, 'analytics/personal_analytics.html', context)


@staff_member_required
def export_interface(request):
    """Export interface for selecting data and formats"""
    track_page_view(request, 'export_interface')

    available_formats = get_available_export_formats()

    # Get available sports for filtering
    from django.db.models import Q
    sports = BettingPattern.objects.values_list('sport', flat=True).distinct()

    # Get available users for filtering (top 100 by activity)
    from django.contrib.auth import get_user_model
    User = get_user_model()

    top_users = User.objects.annotate(
        activity_count=Count('activities')
    ).filter(activity_count__gt=0).order_by('-activity_count')[:100]

    context = {
        'available_formats': available_formats,
        'sports': sports,
        'top_users': top_users,
        'data_types': [
            ('user_activities', 'User Activities'),
            ('betting_patterns', 'Betting Patterns'),
            ('daily_metrics', 'Daily Metrics'),
            ('revenue_reports', 'Revenue Reports'),
            ('sport_popularity', 'Sport Popularity'),
        ]
    }

    return render(request, 'analytics/export_interface.html', context)


@staff_member_required
def transaction_reports(request):
    """Detailed transaction reporting"""
    track_page_view(request, 'transaction_reports')

    # Get filters from request
    days = int(request.GET.get('days', 30))
    user_id = request.GET.get('user_id')
    payment_method = request.GET.get('payment_method')
    transaction_type = request.GET.get('transaction_type')
    min_amount = request.GET.get('min_amount')
    max_amount = request.GET.get('max_amount')

    # Date range
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)

    # Generate transaction report
    transaction_reporter = TransactionReporter(start_date, end_date)

    # Get comprehensive data
    summary = transaction_reporter.get_transaction_summary()
    daily_breakdown = transaction_reporter.get_daily_transaction_breakdown()
    user_analysis = transaction_reporter.get_user_transaction_analysis(100)
    trends = transaction_reporter.get_transaction_trends()

    # Filter user analysis if user_id provided
    if user_id:
        user_analysis = [u for u in user_analysis if u['user_id'] == int(user_id)]

    # Get available payment methods and users for filters
    try:
        from payments.models import Transaction
        payment_methods = Transaction.objects.values_list('payment_method', flat=True).distinct()
    except ImportError:
        payment_methods = ['mpesa', 'stripe', 'bank_transfer']

    from django.contrib.auth import get_user_model
    User = get_user_model()
    top_users = User.objects.annotate(
        activity_count=Count('activities')
    ).filter(activity_count__gt=0).order_by('-activity_count')[:50]

    context = {
        'summary': summary,
        'daily_breakdown': daily_breakdown,
        'user_analysis': user_analysis,
        'trends': trends,
        'payment_methods': payment_methods,
        'top_users': top_users,
        'filters': {
            'days': days,
            'user_id': user_id,
            'payment_method': payment_method,
            'transaction_type': transaction_type,
            'min_amount': min_amount,
            'max_amount': max_amount,
        },
        'date_range': {
            'start': start_date.date(),
            'end': end_date.date()
        }
    }

    return render(request, 'analytics/transaction_reports.html', context)


@staff_member_required
def reconciliation_dashboard(request):
    """Payment reconciliation dashboard"""
    track_page_view(request, 'reconciliation_dashboard')

    days = int(request.GET.get('days', 7))  # Default to last 7 days
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)

    # Get reconciliation data
    reconciliation_data = _get_reconciliation_data(start_date, end_date)

    context = {
        'reconciliation_data': reconciliation_data,
        'days': days,
        'date_range': {
            'start': start_date.date(),
            'end': end_date.date()
        }
    }

    return render(request, 'analytics/reconciliation_dashboard.html', context)


def _get_reconciliation_data(start_date, end_date):
    """Get reconciliation data for payment processing"""
    try:
        from payments.models import Transaction

        # Get transactions in the period
        transactions = Transaction.objects.filter(
            created_at__range=(start_date, end_date)
        )

        # Group by payment method and status
        reconciliation = {}

        for method in ['mpesa', 'stripe', 'bank_transfer']:
            method_transactions = transactions.filter(payment_method=method)

            reconciliation[method] = {
                'total_transactions': method_transactions.count(),
                'completed': method_transactions.filter(status='completed').count(),
                'pending': method_transactions.filter(status='pending').count(),
                'failed': method_transactions.filter(status='failed').count(),
                'total_amount': float(method_transactions.filter(status='completed').aggregate(
                    total=Sum('amount'))['total'] or 0),
                'success_rate': 0
            }

            # Calculate success rate
            total = reconciliation[method]['total_transactions']
            if total > 0:
                reconciliation[method]['success_rate'] = (
                    reconciliation[method]['completed'] / total
                ) * 100

        # Get discrepancies (transactions that might need manual review)
        discrepancies = transactions.filter(
            Q(status='pending', created_at__lt=timezone.now() - timedelta(hours=24)) |
            Q(status='failed', updated_at__lt=timezone.now() - timedelta(hours=1))
        )

        return {
            'by_payment_method': reconciliation,
            'discrepancies': list(discrepancies.values(
                'id', 'payment_method', 'amount', 'status', 'created_at', 'user__phone_number'
            )),
            'total_discrepancies': discrepancies.count()
        }

    except ImportError:
        # Fallback if payments app not available
        return {
            'by_payment_method': {},
            'discrepancies': [],
            'total_discrepancies': 0
        }


@staff_member_required
def profit_loss_dashboard(request):
    """Profit and Loss dashboard"""
    track_page_view(request, 'profit_loss_dashboard')

    days = int(request.GET.get('days', 30))
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)

    # Generate P&L report
    pl_calculator = ProfitLossCalculator(start_date, end_date)
    pl_report = pl_calculator.calculate_profit_loss()
    monthly_comparison = pl_calculator.get_monthly_comparison(6)

    # Calculate period-over-period changes
    previous_period_start = start_date - timedelta(days=days)
    previous_period_end = start_date

    previous_calculator = ProfitLossCalculator(previous_period_start, previous_period_end)
    previous_pl = previous_calculator.calculate_profit_loss()

    # Calculate changes
    revenue_change = 0
    profit_change = 0
    margin_change = 0

    if previous_pl['revenue']['total_revenue'] > 0:
        revenue_change = ((pl_report['revenue']['total_revenue'] - previous_pl['revenue']['total_revenue']) /
                         previous_pl['revenue']['total_revenue']) * 100

    if previous_pl['profit']['net_profit'] != 0:
        profit_change = ((pl_report['profit']['net_profit'] - previous_pl['profit']['net_profit']) /
                        abs(previous_pl['profit']['net_profit'])) * 100

    margin_change = pl_report['margins']['net_margin'] - previous_pl['margins']['net_margin']

    context = {
        'pl_report': pl_report,
        'monthly_comparison': monthly_comparison,
        'changes': {
            'revenue_change': revenue_change,
            'profit_change': profit_change,
            'margin_change': margin_change
        },
        'previous_period': {
            'revenue': previous_pl['revenue']['total_revenue'],
            'profit': previous_pl['profit']['net_profit'],
            'margin': previous_pl['margins']['net_margin']
        },
        'days': days,
        'date_range': {
            'start': start_date.date(),
            'end': end_date.date()
        }
    }

    return render(request, 'analytics/profit_loss_dashboard.html', context)


@staff_member_required
def revenue_analysis(request):
    """Detailed revenue analysis"""
    track_page_view(request, 'revenue_analysis')

    days = int(request.GET.get('days', 30))
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)

    # Get revenue breakdown
    pl_calculator = ProfitLossCalculator(start_date, end_date)
    gaming_revenue = pl_calculator.calculate_gaming_revenue()

    # Get daily revenue trends
    daily_revenue = []
    current_date = start_date.date()

    while current_date <= end_date.date():
        day_start = datetime.combine(current_date, datetime.min.time())
        day_end = datetime.combine(current_date, datetime.max.time())

        day_calculator = ProfitLossCalculator(day_start, day_end)
        day_gaming = day_calculator.calculate_gaming_revenue()

        daily_revenue.append({
            'date': str(current_date),
            'revenue': day_gaming['gross_gaming_revenue'],
            'stakes': day_gaming['total_stakes'],
            'winnings': day_gaming['total_winnings_paid'],
            'bets': day_gaming['total_bets']
        })

        current_date += timedelta(days=1)

    # Get sport performance
    sport_performance = gaming_revenue.get('sport_breakdown', {})

    context = {
        'gaming_revenue': gaming_revenue,
        'daily_revenue': daily_revenue,
        'sport_performance': sport_performance,
        'days': days,
        'date_range': {
            'start': start_date.date(),
            'end': end_date.date()
        }
    }

    return render(request, 'analytics/revenue_analysis.html', context)
