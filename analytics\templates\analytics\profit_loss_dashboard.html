{% extends 'analytics/base.html' %}

{% block title %}Profit & Loss Dashboard{% endblock %}
{% block page_title %}Profit & Loss Dashboard{% endblock %}

{% block content %}
<!-- Key P&L Metrics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card card-metric h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Revenue</div>
                        <div class="h5 mb-0 font-weight-bold">KSh {{ pl_report.revenue.total_revenue|floatformat:0 }}</div>
                        <small class="text-light">
                            {% if changes.revenue_change > 0 %}
                                <i class="fas fa-arrow-up text-success"></i> {{ changes.revenue_change|floatformat:1 }}%
                            {% elif changes.revenue_change < 0 %}
                                <i class="fas fa-arrow-down text-danger"></i> {{ changes.revenue_change|floatformat:1 }}%
                            {% else %}
                                No change
                            {% endif %}
                        </small>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-coins fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card card-metric-warning h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Costs</div>
                        <div class="h5 mb-0 font-weight-bold">KSh {{ pl_report.costs.total_operational_costs|floatformat:0 }}</div>
                        <small class="text-light">{{ pl_report.costs.total_operational_costs|floatformat:1 }}% of revenue</small>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card {% if pl_report.profit.net_profit > 0 %}card-metric-success{% else %}card-metric-danger{% endif %} h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Net Profit</div>
                        <div class="h5 mb-0 font-weight-bold">KSh {{ pl_report.profit.net_profit|floatformat:0 }}</div>
                        <small class="text-light">
                            {% if changes.profit_change > 0 %}
                                <i class="fas fa-arrow-up text-success"></i> {{ changes.profit_change|floatformat:1 }}%
                            {% elif changes.profit_change < 0 %}
                                <i class="fas fa-arrow-down text-danger"></i> {{ changes.profit_change|floatformat:1 }}%
                            {% else %}
                                No change
                            {% endif %}
                        </small>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card card-metric-info h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Net Margin</div>
                        <div class="h5 mb-0 font-weight-bold">{{ pl_report.margins.net_margin|floatformat:1 }}%</div>
                        <small class="text-light">
                            {% if changes.margin_change > 0 %}
                                <i class="fas fa-arrow-up text-success"></i> +{{ changes.margin_change|floatformat:1 }}pp
                            {% elif changes.margin_change < 0 %}
                                <i class="fas fa-arrow-down text-danger"></i> {{ changes.margin_change|floatformat:1 }}pp
                            {% else %}
                                No change
                            {% endif %}
                        </small>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- P&L Statement -->
<div class="row mb-4">
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Profit & Loss Statement</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tbody>
                            <tr class="table-primary">
                                <td><strong>REVENUE</strong></td>
                                <td class="text-end"><strong>KSh {{ pl_report.revenue.total_revenue|floatformat:0 }}</strong></td>
                                <td class="text-end"><strong>100.0%</strong></td>
                            </tr>
                            <tr>
                                <td class="ps-4">Gaming Revenue</td>
                                <td class="text-end">KSh {{ pl_report.revenue.gross_gaming_revenue|floatformat:0 }}</td>
                                <td class="text-end">{{ pl_report.revenue.gross_gaming_revenue|floatformat:1 }}%</td>
                            </tr>
                            <tr>
                                <td class="ps-4">Transaction Fees</td>
                                <td class="text-end">KSh {{ pl_report.revenue.transaction_fees|floatformat:0 }}</td>
                                <td class="text-end">{{ pl_report.revenue.transaction_fees|floatformat:1 }}%</td>
                            </tr>
                            
                            <tr class="table-warning">
                                <td><strong>OPERATING EXPENSES</strong></td>
                                <td class="text-end"><strong>KSh {{ pl_report.costs.total_operational_costs|floatformat:0 }}</strong></td>
                                <td class="text-end"><strong>{{ pl_report.costs.total_operational_costs|floatformat:1 }}%</strong></td>
                            </tr>
                            {% for cost_type, amount in pl_report.costs.operational_costs.items %}
                            <tr>
                                <td class="ps-4">{{ cost_type|title|replace:"_":" " }}</td>
                                <td class="text-end">KSh {{ amount|floatformat:0 }}</td>
                                <td class="text-end">{{ amount|floatformat:1 }}%</td>
                            </tr>
                            {% endfor %}
                            
                            <tr class="table-success">
                                <td><strong>EBITDA</strong></td>
                                <td class="text-end"><strong>KSh {{ pl_report.profit.ebitda|floatformat:0 }}</strong></td>
                                <td class="text-end"><strong>{{ pl_report.margins.operating_margin|floatformat:1 }}%</strong></td>
                            </tr>
                            
                            <tr>
                                <td class="ps-4">Depreciation</td>
                                <td class="text-end">KSh {{ pl_report.costs.depreciation|floatformat:0 }}</td>
                                <td class="text-end">{{ pl_report.costs.depreciation|floatformat:1 }}%</td>
                            </tr>
                            
                            <tr class="table-info">
                                <td><strong>EBIT</strong></td>
                                <td class="text-end"><strong>KSh {{ pl_report.profit.ebit|floatformat:0 }}</strong></td>
                                <td class="text-end"><strong>{{ pl_report.profit.ebit|floatformat:1 }}%</strong></td>
                            </tr>
                            
                            <tr>
                                <td class="ps-4">Interest Expense</td>
                                <td class="text-end">KSh {{ pl_report.costs.interest_expense|floatformat:0 }}</td>
                                <td class="text-end">{{ pl_report.costs.interest_expense|floatformat:1 }}%</td>
                            </tr>
                            
                            <tr>
                                <td><strong>Earnings Before Tax</strong></td>
                                <td class="text-end"><strong>KSh {{ pl_report.profit.ebt|floatformat:0 }}</strong></td>
                                <td class="text-end"><strong>{{ pl_report.profit.ebt|floatformat:1 }}%</strong></td>
                            </tr>
                            
                            <tr>
                                <td class="ps-4">Tax Expense</td>
                                <td class="text-end">KSh {{ pl_report.profit.tax_expense|floatformat:0 }}</td>
                                <td class="text-end">{{ pl_report.profit.tax_expense|floatformat:1 }}%</td>
                            </tr>
                            
                            <tr class="table-dark">
                                <td><strong>NET PROFIT</strong></td>
                                <td class="text-end"><strong>KSh {{ pl_report.profit.net_profit|floatformat:0 }}</strong></td>
                                <td class="text-end"><strong>{{ pl_report.margins.net_margin|floatformat:1 }}%</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Key Performance Indicators -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Key Performance Indicators</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Revenue per User</span>
                        <strong>KSh {{ pl_report.kpis.revenue_per_user|floatformat:0 }}</strong>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Cost per Acquisition</span>
                        <strong>KSh {{ pl_report.kpis.cost_per_acquisition|floatformat:0 }}</strong>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Return on Revenue</span>
                        <strong>{{ pl_report.kpis.return_on_revenue|floatformat:1 }}%</strong>
                    </div>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Total Bets</span>
                        <strong>{{ pl_report.gaming_metrics.total_bets|floatformat:0 }}</strong>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Player Win Rate</span>
                        <strong>{{ pl_report.gaming_metrics.win_rate|floatformat:1 }}%</strong>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>House Edge</span>
                        <strong>{{ pl_report.gaming_metrics.win_rate|floatformat:1 }}%</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Comparison Chart -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Monthly P&L Trends</h6>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="monthlyPLChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Monthly P&L Chart
const monthlyCtx = document.getElementById('monthlyPLChart').getContext('2d');
const monthlyData = {{ monthly_comparison|safe }};

new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: monthlyData.map(d => d.month),
        datasets: [
            {
                label: 'Revenue',
                data: monthlyData.map(d => d.revenue),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1,
                yAxisID: 'y'
            },
            {
                label: 'Costs',
                data: monthlyData.map(d => d.costs),
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1,
                yAxisID: 'y'
            },
            {
                label: 'Profit',
                data: monthlyData.map(d => d.profit),
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                tension: 0.1,
                yAxisID: 'y'
            },
            {
                label: 'Net Margin (%)',
                data: monthlyData.map(d => d.margin),
                borderColor: 'rgb(255, 205, 86)',
                backgroundColor: 'rgba(255, 205, 86, 0.2)',
                tension: 0.1,
                type: 'line',
                yAxisID: 'y1'
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Amount (KSh)'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Margin (%)'
                },
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
});
</script>
{% endblock %}
