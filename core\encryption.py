"""
Field-level encryption utilities for sensitive data
"""

import base64
import hashlib
import os
from typing import Optional, Union
from cryptography.fernet import <PERSON>rne<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from django.conf import settings
from django.core.exceptions import ImproperlyConfigured
import logging

logger = logging.getLogger(__name__)


class FieldEncryption:
    """
    Utility class for encrypting and decrypting sensitive field data
    """
    
    def __init__(self):
        self._fernet = None
        self._initialize_encryption()
    
    def _initialize_encryption(self):
        """Initialize encryption with key from settings"""
        try:
            # Get encryption key from settings
            encryption_key = getattr(settings, 'FIELD_ENCRYPTION_KEY', None)
            
            if not encryption_key:
                # Generate key from SECRET_KEY if no specific encryption key is set
                secret_key = settings.SECRET_KEY.encode()
                salt = b'betika_clone_salt'  # Use a consistent salt
                
                kdf = PBKDF2HMAC(
                    algorithm=hashes.SHA256(),
                    length=32,
                    salt=salt,
                    iterations=100000,
                )
                key = base64.urlsafe_b64encode(kdf.derive(secret_key))
                self._fernet = Fernet(key)
            else:
                # Use provided encryption key
                if isinstance(encryption_key, str):
                    encryption_key = encryption_key.encode()
                
                # Ensure key is properly formatted
                if len(encryption_key) != 44:  # Fernet key length
                    # Derive key from provided string
                    kdf = PBKDF2HMAC(
                        algorithm=hashes.SHA256(),
                        length=32,
                        salt=b'betika_encryption',
                        iterations=100000,
                    )
                    key = base64.urlsafe_b64encode(kdf.derive(encryption_key))
                    self._fernet = Fernet(key)
                else:
                    self._fernet = Fernet(encryption_key)
                    
        except Exception as e:
            logger.error(f"Failed to initialize encryption: {e}")
            raise ImproperlyConfigured("Failed to initialize field encryption")
    
    def encrypt(self, value: Union[str, bytes, None]) -> Optional[str]:
        """
        Encrypt a value and return base64 encoded string
        """
        if value is None or value == '':
            return None
        
        try:
            if isinstance(value, str):
                value = value.encode('utf-8')
            
            encrypted = self._fernet.encrypt(value)
            return base64.urlsafe_b64encode(encrypted).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise ValueError("Failed to encrypt value")
    
    def decrypt(self, encrypted_value: Optional[str]) -> Optional[str]:
        """
        Decrypt a base64 encoded encrypted string
        """
        if encrypted_value is None or encrypted_value == '':
            return None
        
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_value.encode('utf-8'))
            decrypted = self._fernet.decrypt(encrypted_bytes)
            return decrypted.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            # Return None instead of raising exception to handle corrupted data gracefully
            return None
    
    def hash_value(self, value: str) -> str:
        """
        Create a hash of the value for indexing purposes
        """
        if not value:
            return ''
        
        # Use SHA-256 for hashing
        return hashlib.sha256(value.encode('utf-8')).hexdigest()


# Global instance
field_encryption = FieldEncryption()


class EncryptedField:
    """
    Descriptor for encrypted model fields
    """
    
    def __init__(self, field_name: str):
        self.field_name = field_name
        self.encrypted_field_name = f"{field_name}_encrypted"
        self.hash_field_name = f"{field_name}_hash"
    
    def __get__(self, instance, owner):
        if instance is None:
            return self
        
        # Get encrypted value from database
        encrypted_value = getattr(instance, self.encrypted_field_name, None)
        
        if encrypted_value:
            # Decrypt and return
            return field_encryption.decrypt(encrypted_value)
        
        return None
    
    def __set__(self, instance, value):
        if value is None or value == '':
            # Clear encrypted and hash fields
            setattr(instance, self.encrypted_field_name, None)
            setattr(instance, self.hash_field_name, None)
        else:
            # Encrypt value and store
            encrypted_value = field_encryption.encrypt(value)
            setattr(instance, self.encrypted_field_name, encrypted_value)
            
            # Create hash for indexing
            hash_value = field_encryption.hash_value(value)
            setattr(instance, self.hash_field_name, hash_value)


def encrypt_sensitive_data(data: dict, fields_to_encrypt: list) -> dict:
    """
    Encrypt specified fields in a dictionary
    """
    encrypted_data = data.copy()
    
    for field in fields_to_encrypt:
        if field in encrypted_data and encrypted_data[field]:
            encrypted_data[field] = field_encryption.encrypt(encrypted_data[field])
    
    return encrypted_data


def decrypt_sensitive_data(data: dict, fields_to_decrypt: list) -> dict:
    """
    Decrypt specified fields in a dictionary
    """
    decrypted_data = data.copy()
    
    for field in fields_to_decrypt:
        if field in decrypted_data and decrypted_data[field]:
            decrypted_value = field_encryption.decrypt(decrypted_data[field])
            if decrypted_value is not None:
                decrypted_data[field] = decrypted_value
    
    return decrypted_data


class SecureDataMixin:
    """
    Mixin for models that need encrypted fields
    """
    
    def encrypt_field(self, field_name: str, value: str) -> None:
        """
        Encrypt a field value and store it
        """
        if value:
            encrypted_value = field_encryption.encrypt(value)
            setattr(self, f"{field_name}_encrypted", encrypted_value)
            
            # Create hash for searching
            hash_value = field_encryption.hash_value(value)
            setattr(self, f"{field_name}_hash", hash_value)
        else:
            setattr(self, f"{field_name}_encrypted", None)
            setattr(self, f"{field_name}_hash", None)
    
    def decrypt_field(self, field_name: str) -> Optional[str]:
        """
        Decrypt a field value
        """
        encrypted_value = getattr(self, f"{field_name}_encrypted", None)
        if encrypted_value:
            return field_encryption.decrypt(encrypted_value)
        return None
    
    def find_by_encrypted_field(self, field_name: str, value: str):
        """
        Find records by encrypted field using hash
        """
        hash_value = field_encryption.hash_value(value)
        return self.__class__.objects.filter(**{f"{field_name}_hash": hash_value})


def generate_encryption_key() -> str:
    """
    Generate a new Fernet encryption key
    """
    return Fernet.generate_key().decode('utf-8')


def mask_sensitive_data(value: str, mask_char: str = '*', visible_chars: int = 4) -> str:
    """
    Mask sensitive data for display purposes
    """
    if not value or len(value) <= visible_chars:
        return mask_char * len(value) if value else ''
    
    # Show first and last few characters
    visible_start = visible_chars // 2
    visible_end = visible_chars - visible_start
    
    masked_middle = mask_char * (len(value) - visible_chars)
    
    return value[:visible_start] + masked_middle + value[-visible_end:]


def secure_compare(value1: str, value2: str) -> bool:
    """
    Securely compare two strings to prevent timing attacks
    """
    if not value1 or not value2:
        return value1 == value2
    
    # Use constant-time comparison
    import hmac
    return hmac.compare_digest(value1, value2)
