{% extends 'analytics/base.html' %}

{% block title %}Data Export{% endblock %}
{% block page_title %}Data Export Interface{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Export Analytics Data</h6>
            </div>
            <div class="card-body">
                <form id="exportForm" method="get" action="{% url 'analytics:export_data' %}">
                    <div class="row">
                        <!-- Data Type Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="dataType" class="form-label">Data Type</label>
                            <select class="form-select" id="dataType" name="type" required>
                                {% for value, label in data_types %}
                                <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- Format Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="format" class="form-label">Export Format</label>
                            <select class="form-select" id="format" name="format" required>
                                {% for format in available_formats %}
                                <option value="{{ format }}">{{ format|upper }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Date Range -->
                        <div class="col-md-6 mb-3">
                            <label for="days" class="form-label">Time Period</label>
                            <select class="form-select" id="days" name="days">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 90 days</option>
                                <option value="365">Last year</option>
                            </select>
                        </div>
                        
                        <!-- Report Type (for revenue reports) -->
                        <div class="col-md-6 mb-3" id="reportTypeDiv" style="display: none;">
                            <label for="reportType" class="form-label">Report Type</label>
                            <select class="form-select" id="reportType" name="report_type">
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly" selected>Monthly</option>
                                <option value="quarterly">Quarterly</option>
                                <option value="yearly">Yearly</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Filters -->
                    <div class="row">
                        <!-- User Filter -->
                        <div class="col-md-6 mb-3" id="userFilterDiv" style="display: none;">
                            <label for="userFilter" class="form-label">Filter by User</label>
                            <select class="form-select" id="userFilter" name="user_id">
                                <option value="">All Users</option>
                                {% for user in top_users %}
                                <option value="{{ user.id }}">{{ user.phone_number }} ({{ user.activity_count }} activities)</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <!-- Sport Filter -->
                        <div class="col-md-6 mb-3" id="sportFilterDiv" style="display: none;">
                            <label for="sportFilter" class="form-label">Filter by Sport</label>
                            <select class="form-select" id="sportFilter" name="sport">
                                <option value="">All Sports</option>
                                {% for sport in sports %}
                                <option value="{{ sport }}">{{ sport }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <!-- Export Options -->
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeMetadata" name="include_metadata" checked>
                                <label class="form-check-label" for="includeMetadata">
                                    Include metadata and timestamps
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Export Button -->
                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-download"></i> Export Data
                            </button>
                            <button type="button" class="btn btn-secondary btn-lg ms-2" onclick="previewData()">
                                <i class="fas fa-eye"></i> Preview Data
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Data Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="exportFromPreview()">Export This Data</button>
            </div>
        </div>
    </div>
</div>

<!-- Export History -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Exports</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Data Type</th>
                                <th>Format</th>
                                <th>Date Range</th>
                                <th>Exported At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="exportHistory">
                            <tr>
                                <td colspan="5" class="text-center text-muted">No recent exports</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dataTypeSelect = document.getElementById('dataType');
    const reportTypeDiv = document.getElementById('reportTypeDiv');
    const userFilterDiv = document.getElementById('userFilterDiv');
    const sportFilterDiv = document.getElementById('sportFilterDiv');
    
    // Show/hide filters based on data type
    dataTypeSelect.addEventListener('change', function() {
        const dataType = this.value;
        
        // Hide all filters first
        reportTypeDiv.style.display = 'none';
        userFilterDiv.style.display = 'none';
        sportFilterDiv.style.display = 'none';
        
        // Show relevant filters
        if (dataType === 'revenue_reports') {
            reportTypeDiv.style.display = 'block';
        }
        
        if (dataType === 'user_activities') {
            userFilterDiv.style.display = 'block';
        }
        
        if (dataType === 'betting_patterns' || dataType === 'sport_popularity') {
            sportFilterDiv.style.display = 'block';
        }
    });
    
    // Load export history
    loadExportHistory();
});

function previewData() {
    const form = document.getElementById('exportForm');
    const formData = new FormData(form);
    const params = new URLSearchParams(formData);
    params.append('preview', 'true');
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
    
    // Load preview data
    fetch(`{% url 'analytics:api_data' %}?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('previewContent').innerHTML = `
                <div class="alert alert-info">
                    <strong>Preview:</strong> Showing first 10 records of ${data.total || 'unknown'} total records
                </div>
                <pre class="bg-light p-3" style="max-height: 400px; overflow-y: auto;">${JSON.stringify(data, null, 2)}</pre>
            `;
        })
        .catch(error => {
            document.getElementById('previewContent').innerHTML = `
                <div class="alert alert-danger">
                    <strong>Error:</strong> Failed to load preview data
                </div>
            `;
        });
}

function exportFromPreview() {
    document.getElementById('exportForm').submit();
}

function loadExportHistory() {
    // This would load recent export history from localStorage or API
    const history = JSON.parse(localStorage.getItem('exportHistory') || '[]');
    const tbody = document.getElementById('exportHistory');
    
    if (history.length === 0) {
        return;
    }
    
    tbody.innerHTML = history.map(item => `
        <tr>
            <td>${item.dataType}</td>
            <td>${item.format.toUpperCase()}</td>
            <td>${item.dateRange}</td>
            <td>${new Date(item.exportedAt).toLocaleString()}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="repeatExport('${item.id}')">
                    <i class="fas fa-redo"></i> Repeat
                </button>
            </td>
        </tr>
    `).join('');
}

function repeatExport(exportId) {
    // This would repeat a previous export
    alert('Repeat export functionality would be implemented here');
}

// Save export to history when form is submitted
document.getElementById('exportForm').addEventListener('submit', function() {
    const formData = new FormData(this);
    const exportRecord = {
        id: Date.now().toString(),
        dataType: formData.get('type'),
        format: formData.get('format'),
        dateRange: `Last ${formData.get('days')} days`,
        exportedAt: new Date().toISOString()
    };
    
    const history = JSON.parse(localStorage.getItem('exportHistory') || '[]');
    history.unshift(exportRecord);
    history.splice(10); // Keep only last 10 exports
    localStorage.setItem('exportHistory', JSON.stringify(history));
});
</script>
{% endblock %}
