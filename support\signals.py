"""
Django signals for support system notifications
"""

import logging
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone

from .models import SupportTicket, TicketResponse, ChatSession, ChatMessage, SupportNotification

logger = logging.getLogger(__name__)
User = get_user_model()


@receiver(post_save, sender=SupportTicket)
def ticket_created_notification(sender, instance, created, **kwargs):
    """Send notification when a new ticket is created"""
    if created:
        try:
            # Notify all staff members about new ticket
            staff_users = User.objects.filter(is_staff=True, is_active=True)
            
            for staff_user in staff_users:
                SupportNotification.objects.create(
                    recipient=staff_user,
                    notification_type='ticket_created',
                    title=f'New Support Ticket: {instance.ticket_number}',
                    message=f'A new support ticket has been created by {instance.user.get_full_name() or instance.user.phone_number}: {instance.subject}',
                    ticket=instance
                )
            
            logger.info(f'Notifications sent for new ticket: {instance.ticket_number}')
            
        except Exception as e:
            logger.error(f'Error sending ticket creation notifications: {e}')


@receiver(pre_save, sender=SupportTicket)
def ticket_status_change_notification(sender, instance, **kwargs):
    """Send notification when ticket status changes"""
    if instance.pk:  # Only for existing tickets
        try:
            old_ticket = SupportTicket.objects.get(pk=instance.pk)
            
            # Check if status changed
            if old_ticket.status != instance.status:
                # Notify the ticket owner
                SupportNotification.objects.create(
                    recipient=instance.user,
                    notification_type='ticket_updated',
                    title=f'Ticket Status Updated: {instance.ticket_number}',
                    message=f'Your support ticket status has been changed from {dict(SupportTicket.STATUS_CHOICES).get(old_ticket.status, old_ticket.status)} to {dict(SupportTicket.STATUS_CHOICES).get(instance.status, instance.status)}',
                    ticket=instance
                )
                
                # If ticket is assigned, notify the assigned agent
                if instance.assigned_to and old_ticket.assigned_to != instance.assigned_to:
                    SupportNotification.objects.create(
                        recipient=instance.assigned_to,
                        notification_type='ticket_updated',
                        title=f'Ticket Assigned: {instance.ticket_number}',
                        message=f'You have been assigned to support ticket: {instance.subject}',
                        ticket=instance
                    )
                
                logger.info(f'Status change notifications sent for ticket: {instance.ticket_number}')
                
        except SupportTicket.DoesNotExist:
            pass  # New ticket, handled by post_save
        except Exception as e:
            logger.error(f'Error sending ticket status change notifications: {e}')


@receiver(post_save, sender=TicketResponse)
def ticket_response_notification(sender, instance, created, **kwargs):
    """Send notification when a new response is added to a ticket"""
    if created and not instance.is_internal_note:
        try:
            # Determine recipient based on who sent the response
            if instance.is_staff_response:
                # Staff responded, notify the customer
                recipient = instance.ticket.user
                title = f'New Response to Ticket: {instance.ticket.ticket_number}'
                message = f'A support agent has responded to your ticket: {instance.ticket.subject}'
            else:
                # Customer responded, notify assigned agent or all staff
                if instance.ticket.assigned_to:
                    recipient = instance.ticket.assigned_to
                else:
                    # Notify all staff if no specific agent assigned
                    staff_users = User.objects.filter(is_staff=True, is_active=True)
                    for staff_user in staff_users:
                        SupportNotification.objects.create(
                            recipient=staff_user,
                            notification_type='ticket_response',
                            title=f'Customer Response: {instance.ticket.ticket_number}',
                            message=f'Customer has responded to ticket: {instance.ticket.subject}',
                            ticket=instance.ticket
                        )
                    return
                
                title = f'Customer Response: {instance.ticket.ticket_number}'
                message = f'Customer has responded to ticket: {instance.ticket.subject}'
            
            # Create notification for the recipient
            SupportNotification.objects.create(
                recipient=recipient,
                notification_type='ticket_response',
                title=title,
                message=message,
                ticket=instance.ticket
            )
            
            logger.info(f'Response notification sent for ticket: {instance.ticket.ticket_number}')
            
        except Exception as e:
            logger.error(f'Error sending ticket response notifications: {e}')


@receiver(post_save, sender=ChatSession)
def chat_session_notification(sender, instance, created, **kwargs):
    """Send notification when a new chat session is created"""
    if created:
        try:
            # Notify all available staff members about new chat
            available_staff = User.objects.filter(is_staff=True, is_active=True)
            
            for staff_user in available_staff:
                SupportNotification.objects.create(
                    recipient=staff_user,
                    notification_type='chat_started',
                    title='New Chat Session',
                    message=f'A new chat session is waiting for an agent: {instance.session_id}',
                    chat_session=instance
                )
            
            logger.info(f'Chat session notifications sent: {instance.session_id}')
            
        except Exception as e:
            logger.error(f'Error sending chat session notifications: {e}')


@receiver(pre_save, sender=ChatSession)
def chat_session_status_change_notification(sender, instance, **kwargs):
    """Send notification when chat session status changes"""
    if instance.pk:  # Only for existing sessions
        try:
            old_session = ChatSession.objects.get(pk=instance.pk)
            
            # Check if status changed to active (agent joined)
            if old_session.status == 'waiting' and instance.status == 'active':
                # Notify the customer that an agent has joined
                SupportNotification.objects.create(
                    recipient=instance.user,
                    notification_type='chat_started',
                    title='Agent Joined Chat',
                    message=f'A support agent has joined your chat session: {instance.session_id}',
                    chat_session=instance
                )
                
                logger.info(f'Agent joined notification sent for chat: {instance.session_id}')
            
            # Check if session ended
            elif old_session.status in ['waiting', 'active'] and instance.status == 'ended':
                # Notify both participants that the session ended
                participants = [instance.user]
                if instance.agent:
                    participants.append(instance.agent)
                
                for participant in participants:
                    SupportNotification.objects.create(
                        recipient=participant,
                        notification_type='chat_ended',
                        title='Chat Session Ended',
                        message=f'Your chat session has ended: {instance.session_id}',
                        chat_session=instance
                    )
                
                logger.info(f'Session ended notifications sent for chat: {instance.session_id}')
                
        except ChatSession.DoesNotExist:
            pass  # New session, handled by post_save
        except Exception as e:
            logger.error(f'Error sending chat session status change notifications: {e}')


@receiver(post_save, sender=ChatMessage)
def chat_message_notification(sender, instance, created, **kwargs):
    """Send notification when a new chat message is sent"""
    if created and not instance.is_system_message:
        try:
            # Determine recipient (the other participant in the chat)
            if instance.sender == instance.session.user:
                # Customer sent message, notify agent
                recipient = instance.session.agent
            else:
                # Agent sent message, notify customer
                recipient = instance.session.user
            
            if recipient:
                SupportNotification.objects.create(
                    recipient=recipient,
                    notification_type='chat_message',
                    title=f'New Chat Message: {instance.session.session_id}',
                    message=f'You have a new message in chat session: {instance.session.session_id}',
                    chat_session=instance.session
                )
                
                logger.info(f'Chat message notification sent for session: {instance.session.session_id}')
            
        except Exception as e:
            logger.error(f'Error sending chat message notifications: {e}')


@receiver(post_save, sender=SupportNotification)
def mark_notification_as_sent(sender, instance, created, **kwargs):
    """Mark notification as sent after creation"""
    if created and not instance.is_sent:
        try:
            instance.mark_as_sent()
            logger.debug(f'Notification marked as sent: {instance.id}')
            
        except Exception as e:
            logger.error(f'Error marking notification as sent: {e}')


# Cleanup old notifications periodically
def cleanup_old_notifications():
    """Clean up old read notifications (older than 30 days)"""
    try:
        from datetime import timedelta
        
        cutoff_date = timezone.now() - timedelta(days=30)
        
        old_notifications = SupportNotification.objects.filter(
            is_read=True,
            read_at__lt=cutoff_date
        )
        
        count = old_notifications.count()
        old_notifications.delete()
        
        logger.info(f'Cleaned up {count} old notifications')
        
    except Exception as e:
        logger.error(f'Error cleaning up old notifications: {e}')


# Auto-escalate old tickets
def auto_escalate_old_tickets():
    """Automatically escalate tickets that have been open for too long"""
    try:
        from datetime import timedelta
        
        # Escalate tickets open for more than 48 hours without response
        cutoff_date = timezone.now() - timedelta(hours=48)
        
        old_tickets = SupportTicket.objects.filter(
            status='open',
            created_at__lt=cutoff_date,
            responses__isnull=True
        )
        
        for ticket in old_tickets:
            ticket.escalate_ticket()
            
            # Create escalation notification
            SupportNotification.objects.create(
                recipient=ticket.user,
                notification_type='ticket_updated',
                title=f'Ticket Escalated: {ticket.ticket_number}',
                message=f'Your support ticket has been escalated due to extended wait time: {ticket.subject}',
                ticket=ticket
            )
        
        logger.info(f'Auto-escalated {old_tickets.count()} tickets')
        
    except Exception as e:
        logger.error(f'Error auto-escalating tickets: {e}')


# Close abandoned chat sessions
def close_abandoned_chat_sessions():
    """Close chat sessions that have been inactive for too long"""
    try:
        from datetime import timedelta
        
        # Close sessions inactive for more than 30 minutes
        cutoff_date = timezone.now() - timedelta(minutes=30)
        
        abandoned_sessions = ChatSession.objects.filter(
            status__in=['waiting', 'active'],
            last_activity_at__lt=cutoff_date
        )
        
        for session in abandoned_sessions:
            session.status = 'abandoned'
            session.ended_at = timezone.now()
            session.save()
            
            # Notify participants
            if session.user:
                SupportNotification.objects.create(
                    recipient=session.user,
                    notification_type='chat_ended',
                    title='Chat Session Closed',
                    message=f'Your chat session was closed due to inactivity: {session.session_id}',
                    chat_session=session
                )
        
        logger.info(f'Closed {abandoned_sessions.count()} abandoned chat sessions')
        
    except Exception as e:
        logger.error(f'Error closing abandoned chat sessions: {e}')
