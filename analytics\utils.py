"""
Analytics utilities and helper functions
"""

from django.db.models import Sum, Count, Avg, Q
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
import json

from .models import (
    UserActivity, BettingPattern, DailyMetrics, 
    SportPopularity, UserSegment, RevenueReport
)

User = get_user_model()


def get_client_ip_from_request(request):
    """Extract client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def calculate_daily_metrics(date=None):
    """Calculate and update daily metrics for a specific date"""
    if date is None:
        date = timezone.now().date()
    
    start_datetime = datetime.combine(date, datetime.min.time())
    end_datetime = datetime.combine(date, datetime.max.time())
    
    # Get or create daily metrics record
    metrics, created = DailyMetrics.objects.get_or_create(date=date)
    
    # Calculate user metrics
    new_registrations = User.objects.filter(
        date_joined__range=(start_datetime, end_datetime)
    ).count()
    
    active_users = UserActivity.objects.filter(
        timestamp__range=(start_datetime, end_datetime),
        user__isnull=False
    ).values('user').distinct().count()
    
    # Calculate betting metrics
    bet_activities = UserActivity.objects.filter(
        timestamp__range=(start_datetime, end_datetime),
        action_type='bet_placed'
    )
    
    total_bets_placed = bet_activities.count()
    
    # Calculate financial metrics from activities
    deposit_activities = UserActivity.objects.filter(
        timestamp__range=(start_datetime, end_datetime),
        action_type='deposit'
    )
    
    withdrawal_activities = UserActivity.objects.filter(
        timestamp__range=(start_datetime, end_datetime),
        action_type='withdrawal'
    )
    
    # Calculate page views
    page_views = UserActivity.objects.filter(
        timestamp__range=(start_datetime, end_datetime),
        action_type='page_view'
    ).count()
    
    unique_visitors = UserActivity.objects.filter(
        timestamp__range=(start_datetime, end_datetime),
        action_type='page_view'
    ).values('session_id').distinct().count()
    
    # Update metrics
    metrics.new_registrations = new_registrations
    metrics.active_users = active_users
    metrics.total_bets_placed = total_bets_placed
    metrics.page_views = page_views
    metrics.unique_visitors = unique_visitors
    
    # Calculate bounce rate (simplified - users with only one page view)
    single_page_sessions = UserActivity.objects.filter(
        timestamp__range=(start_datetime, end_datetime),
        action_type='page_view'
    ).values('session_id').annotate(
        page_count=Count('id')
    ).filter(page_count=1).count()
    
    if unique_visitors > 0:
        metrics.bounce_rate = (single_page_sessions / unique_visitors) * 100
    
    metrics.save()
    return metrics


def get_user_analytics_summary(user, days=30):
    """Get analytics summary for a specific user"""
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)
    
    activities = UserActivity.objects.filter(
        user=user,
        timestamp__range=(start_date, end_date)
    )
    
    # Basic activity counts
    activity_counts = activities.values('action_type').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Betting patterns
    betting_patterns = BettingPattern.objects.filter(user=user)
    
    # Recent activities
    recent_activities = activities.order_by('-timestamp')[:10]
    
    # User segments
    segments = UserSegment.objects.filter(user=user, is_active=True)
    
    return {
        'total_activities': activities.count(),
        'activity_breakdown': list(activity_counts),
        'betting_patterns': list(betting_patterns.values()),
        'recent_activities': [
            {
                'action': activity.get_action_type_display(),
                'timestamp': activity.timestamp,
                'metadata': activity.metadata
            }
            for activity in recent_activities
        ],
        'segments': [segment.get_segment_type_display() for segment in segments],
        'period_days': days
    }


def get_platform_analytics_summary(days=30):
    """Get platform-wide analytics summary"""
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)
    
    # Get daily metrics for the period
    daily_metrics = DailyMetrics.objects.filter(
        date__range=(start_date.date(), end_date.date())
    ).order_by('date')
    
    # Aggregate totals
    totals = daily_metrics.aggregate(
        total_registrations=Sum('new_registrations'),
        total_bets=Sum('total_bets_placed'),
        total_bet_amount=Sum('total_bet_amount'),
        total_deposits=Sum('total_deposits'),
        total_withdrawals=Sum('total_withdrawals'),
        avg_active_users=Avg('active_users'),
        total_page_views=Sum('page_views'),
    )
    
    # Sport popularity
    sport_popularity = SportPopularity.objects.filter(
        date__range=(start_date.date(), end_date.date())
    ).values('sport').annotate(
        total_bets=Sum('total_bets'),
        total_amount=Sum('total_amount_bet')
    ).order_by('-total_bets')[:10]
    
    # User segments distribution
    segment_distribution = UserSegment.objects.filter(
        is_active=True
    ).values('segment_type').annotate(
        count=Count('id')
    ).order_by('-count')
    
    return {
        'period_days': days,
        'totals': totals,
        'daily_metrics': list(daily_metrics.values()),
        'sport_popularity': list(sport_popularity),
        'segment_distribution': list(segment_distribution),
        'growth_metrics': _calculate_growth_metrics(daily_metrics)
    }


def _calculate_growth_metrics(daily_metrics):
    """Calculate growth metrics from daily data"""
    if not daily_metrics:
        return {}
    
    metrics_list = list(daily_metrics)
    if len(metrics_list) < 2:
        return {}
    
    first_week = metrics_list[:7] if len(metrics_list) >= 7 else metrics_list[:len(metrics_list)//2]
    last_week = metrics_list[-7:] if len(metrics_list) >= 7 else metrics_list[len(metrics_list)//2:]
    
    first_week_avg_users = sum(m.active_users for m in first_week) / len(first_week)
    last_week_avg_users = sum(m.active_users for m in last_week) / len(last_week)
    
    user_growth = 0
    if first_week_avg_users > 0:
        user_growth = ((last_week_avg_users - first_week_avg_users) / first_week_avg_users) * 100
    
    return {
        'user_growth_percentage': round(user_growth, 2),
        'first_period_avg_users': round(first_week_avg_users, 2),
        'last_period_avg_users': round(last_week_avg_users, 2)
    }


def update_user_segments():
    """Update user segments based on current behavior"""
    # High value customers (high betting volume)
    high_value_users = User.objects.annotate(
        total_bet_amount=Sum('betting_patterns__total_amount_bet')
    ).filter(total_bet_amount__gte=10000)
    
    _assign_segment(high_value_users, 'high_value')
    
    # Frequent bettors (many bets in last 30 days)
    thirty_days_ago = timezone.now() - timedelta(days=30)
    frequent_bettors = User.objects.annotate(
        recent_bets=Count('activities', filter=Q(
            activities__action_type='bet_placed',
            activities__timestamp__gte=thirty_days_ago
        ))
    ).filter(recent_bets__gte=20)
    
    _assign_segment(frequent_bettors, 'frequent_bettor')
    
    # New users (registered in last 7 days)
    seven_days_ago = timezone.now() - timedelta(days=7)
    new_users = User.objects.filter(date_joined__gte=seven_days_ago)
    
    _assign_segment(new_users, 'new_user')
    
    # Inactive users (no activity in last 30 days)
    inactive_users = User.objects.exclude(
        activities__timestamp__gte=thirty_days_ago
    ).filter(date_joined__lt=thirty_days_ago)
    
    _assign_segment(inactive_users, 'inactive')


def _assign_segment(users, segment_type):
    """Assign segment to users"""
    for user in users:
        UserSegment.objects.update_or_create(
            user=user,
            segment_type=segment_type,
            defaults={
                'is_active': True,
                'confidence_score': 1.0,
                'criteria': {'auto_assigned': True, 'timestamp': timezone.now().isoformat()}
            }
        )


def generate_revenue_report(report_type='daily', start_date=None, end_date=None):
    """Generate revenue report for specified period"""
    if start_date is None:
        if report_type == 'daily':
            start_date = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
            end_date = start_date + timedelta(days=1)
        elif report_type == 'weekly':
            start_date = timezone.now() - timedelta(days=7)
            end_date = timezone.now()
        elif report_type == 'monthly':
            start_date = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            end_date = timezone.now()
    
    # Get daily metrics for the period
    daily_metrics = DailyMetrics.objects.filter(
        date__range=(start_date.date(), end_date.date())
    )
    
    # Calculate totals
    totals = daily_metrics.aggregate(
        total_bet_amount=Sum('total_bet_amount'),
        total_winnings_paid=Sum('total_winnings_paid'),
        total_deposits=Sum('total_deposits'),
        total_withdrawals=Sum('total_withdrawals'),
        gross_gaming_revenue=Sum('gross_gaming_revenue')
    )
    
    # Create or update revenue report
    report, created = RevenueReport.objects.update_or_create(
        report_type=report_type,
        period_start=start_date,
        period_end=end_date,
        defaults={
            'gross_gaming_revenue': totals['gross_gaming_revenue'] or Decimal('0.00'),
            'total_deposits': totals['total_deposits'] or Decimal('0.00'),
            'total_withdrawals': totals['total_withdrawals'] or Decimal('0.00'),
            'net_gaming_revenue': (totals['gross_gaming_revenue'] or Decimal('0.00')) - (totals['total_winnings_paid'] or Decimal('0.00')),
        }
    )
    
    # Calculate profit margin
    if report.gross_gaming_revenue > 0:
        report.profit_margin = float((report.net_gaming_revenue / report.gross_gaming_revenue) * 100)
        report.save(update_fields=['profit_margin'])
    
    return report


def export_analytics_data(data_type, start_date, end_date, format='json'):
    """Export analytics data in specified format"""
    data = {}
    
    if data_type == 'user_activities':
        activities = UserActivity.objects.filter(
            timestamp__range=(start_date, end_date)
        ).select_related('user').values(
            'user__phone_number', 'action_type', 'timestamp', 
            'ip_address', 'metadata'
        )
        data['user_activities'] = list(activities)
    
    elif data_type == 'daily_metrics':
        metrics = DailyMetrics.objects.filter(
            date__range=(start_date.date(), end_date.date())
        ).values()
        data['daily_metrics'] = list(metrics)
    
    elif data_type == 'betting_patterns':
        patterns = BettingPattern.objects.select_related('user').values(
            'user__phone_number', 'sport', 'bet_type', 'total_bets',
            'total_amount_bet', 'total_winnings', 'win_rate'
        )
        data['betting_patterns'] = list(patterns)
    
    if format == 'json':
        return json.dumps(data, default=str, indent=2)
    elif format == 'csv':
        # CSV export would require pandas or manual CSV generation
        # For now, return JSON
        return json.dumps(data, default=str)
    
    return data
