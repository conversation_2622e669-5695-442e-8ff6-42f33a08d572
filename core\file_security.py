"""
Secure file upload handling with validation and virus scanning
"""

import os
import mimetypes
import hashlib
import magic
from typing import List, Tuple, Optional, Dict
from django.core.files.uploadedfile import UploadedFile
from django.core.exceptions import ValidationError
from django.conf import settings
from django.utils.text import get_valid_filename
from PIL import Image
import logging

logger = logging.getLogger(__name__)


class SecureFileUploadHandler:
    """
    Secure file upload handler with comprehensive validation
    """
    
    # Allowed file types and their MIME types
    ALLOWED_FILE_TYPES = {
        'images': {
            'extensions': ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'mime_types': ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            'max_size': 5 * 1024 * 1024,  # 5MB
        },
        'documents': {
            'extensions': ['pdf', 'doc', 'docx', 'txt'],
            'mime_types': ['application/pdf', 'application/msword', 
                          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                          'text/plain'],
            'max_size': 10 * 1024 * 1024,  # 10MB
        },
        'profile_images': {
            'extensions': ['jpg', 'jpeg', 'png'],
            'mime_types': ['image/jpeg', 'image/png'],
            'max_size': 2 * 1024 * 1024,  # 2MB
        }
    }
    
    # Dangerous file extensions that should never be allowed
    DANGEROUS_EXTENSIONS = [
        'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar',
        'php', 'asp', 'aspx', 'jsp', 'py', 'pl', 'sh', 'ps1'
    ]
    
    # Magic bytes for common file types
    MAGIC_BYTES = {
        'jpg': [b'\xff\xd8\xff'],
        'png': [b'\x89\x50\x4e\x47'],
        'gif': [b'\x47\x49\x46\x38'],
        'pdf': [b'\x25\x50\x44\x46'],
        'zip': [b'\x50\x4b\x03\x04', b'\x50\x4b\x05\x06', b'\x50\x4b\x07\x08']
    }
    
    def __init__(self, file_category: str = 'documents'):
        self.file_category = file_category
        self.config = self.ALLOWED_FILE_TYPES.get(file_category, self.ALLOWED_FILE_TYPES['documents'])
    
    def validate_file(self, uploaded_file: UploadedFile) -> Tuple[bool, Optional[str]]:
        """
        Comprehensive file validation
        """
        try:
            # Basic validation
            if not uploaded_file:
                return False, "No file provided"
            
            # Check file size
            if uploaded_file.size > self.config['max_size']:
                max_size_mb = self.config['max_size'] / (1024 * 1024)
                return False, f"File size exceeds maximum allowed size of {max_size_mb}MB"
            
            # Check file extension
            file_extension = self._get_file_extension(uploaded_file.name)
            if not self._validate_extension(file_extension):
                return False, f"File type '{file_extension}' is not allowed"
            
            # Check for dangerous extensions
            if file_extension.lower() in self.DANGEROUS_EXTENSIONS:
                return False, "File type is potentially dangerous and not allowed"
            
            # Validate MIME type
            if not self._validate_mime_type(uploaded_file):
                return False, "File MIME type is not allowed or doesn't match extension"
            
            # Validate file content (magic bytes)
            if not self._validate_file_content(uploaded_file):
                return False, "File content doesn't match its extension"
            
            # Validate filename
            if not self._validate_filename(uploaded_file.name):
                return False, "Filename contains invalid characters"
            
            # Additional validation for images
            if self.file_category in ['images', 'profile_images']:
                is_valid, error = self._validate_image(uploaded_file)
                if not is_valid:
                    return False, error
            
            # Scan for malicious content
            if not self._scan_for_malicious_content(uploaded_file):
                return False, "File contains potentially malicious content"
            
            return True, None
            
        except Exception as e:
            logger.error(f"File validation error: {e}")
            return False, "File validation failed"
    
    def _get_file_extension(self, filename: str) -> str:
        """Get file extension from filename"""
        return filename.split('.')[-1].lower() if '.' in filename else ''
    
    def _validate_extension(self, extension: str) -> bool:
        """Validate file extension"""
        return extension.lower() in self.config['extensions']
    
    def _validate_mime_type(self, uploaded_file: UploadedFile) -> bool:
        """Validate MIME type"""
        # Get MIME type from file content
        try:
            uploaded_file.seek(0)
            file_content = uploaded_file.read(1024)  # Read first 1KB
            uploaded_file.seek(0)
            
            # Use python-magic to detect MIME type
            detected_mime = magic.from_buffer(file_content, mime=True)
            
            # Check if detected MIME type is allowed
            return detected_mime in self.config['mime_types']
            
        except Exception as e:
            logger.warning(f"MIME type validation failed: {e}")
            # Fallback to Django's content type detection
            content_type = uploaded_file.content_type
            return content_type in self.config['mime_types']
    
    def _validate_file_content(self, uploaded_file: UploadedFile) -> bool:
        """Validate file content using magic bytes"""
        try:
            uploaded_file.seek(0)
            file_header = uploaded_file.read(16)  # Read first 16 bytes
            uploaded_file.seek(0)
            
            file_extension = self._get_file_extension(uploaded_file.name)
            
            # Check magic bytes for known file types
            if file_extension in self.MAGIC_BYTES:
                magic_bytes_list = self.MAGIC_BYTES[file_extension]
                return any(file_header.startswith(magic_bytes) for magic_bytes in magic_bytes_list)
            
            return True  # Allow files without specific magic byte checks
            
        except Exception as e:
            logger.warning(f"File content validation failed: {e}")
            return False
    
    def _validate_filename(self, filename: str) -> bool:
        """Validate filename for security"""
        if not filename:
            return False
        
        # Check for path traversal attempts
        dangerous_patterns = ['../', '..\\', '<', '>', '|', ':', '*', '?', '"']
        for pattern in dangerous_patterns:
            if pattern in filename:
                return False
        
        # Check filename length
        if len(filename) > 255:
            return False
        
        # Ensure filename is valid
        valid_filename = get_valid_filename(filename)
        return valid_filename == filename
    
    def _validate_image(self, uploaded_file: UploadedFile) -> Tuple[bool, Optional[str]]:
        """Additional validation for image files"""
        try:
            uploaded_file.seek(0)
            
            # Try to open with PIL
            with Image.open(uploaded_file) as img:
                # Check image format
                if img.format.lower() not in ['jpeg', 'png', 'gif', 'webp']:
                    return False, "Unsupported image format"
                
                # Check image dimensions
                max_width = 4096
                max_height = 4096
                
                if img.width > max_width or img.height > max_height:
                    return False, f"Image dimensions exceed maximum allowed size ({max_width}x{max_height})"
                
                # Check for suspicious metadata
                if hasattr(img, '_getexif') and img._getexif():
                    # Remove EXIF data for privacy
                    pass
            
            uploaded_file.seek(0)
            return True, None
            
        except Exception as e:
            logger.warning(f"Image validation failed: {e}")
            return False, "Invalid image file"
    
    def _scan_for_malicious_content(self, uploaded_file: UploadedFile) -> bool:
        """
        Basic malicious content scanning
        Note: In production, integrate with a proper antivirus solution
        """
        try:
            uploaded_file.seek(0)
            content = uploaded_file.read()
            uploaded_file.seek(0)
            
            # Check for suspicious patterns
            suspicious_patterns = [
                b'<script',
                b'javascript:',
                b'vbscript:',
                b'onload=',
                b'onerror=',
                b'<?php',
                b'<%',
                b'eval(',
                b'exec(',
                b'system(',
                b'shell_exec('
            ]
            
            content_lower = content.lower()
            for pattern in suspicious_patterns:
                if pattern in content_lower:
                    logger.warning(f"Suspicious pattern found in file: {pattern}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Malicious content scan failed: {e}")
            return False
    
    def generate_secure_filename(self, original_filename: str, user_id: int = None) -> str:
        """
        Generate a secure filename
        """
        # Get file extension
        extension = self._get_file_extension(original_filename)
        
        # Generate hash of original filename + timestamp
        import time
        hash_input = f"{original_filename}_{time.time()}_{user_id or 0}"
        file_hash = hashlib.sha256(hash_input.encode()).hexdigest()[:16]
        
        # Create secure filename
        secure_filename = f"{file_hash}.{extension}"
        
        return secure_filename
    
    def get_upload_path(self, filename: str, user_id: int = None) -> str:
        """
        Generate secure upload path
        """
        # Create directory structure based on file category and user
        if user_id:
            path = f"{self.file_category}/{user_id % 1000}/{user_id}/{filename}"
        else:
            path = f"{self.file_category}/public/{filename}"
        
        return path


def validate_uploaded_file(uploaded_file: UploadedFile, file_category: str = 'documents') -> Tuple[bool, Optional[str]]:
    """
    Convenience function for file validation
    """
    handler = SecureFileUploadHandler(file_category)
    return handler.validate_file(uploaded_file)


def create_secure_upload_path(filename: str, file_category: str = 'documents', user_id: int = None) -> str:
    """
    Create secure upload path for file
    """
    handler = SecureFileUploadHandler(file_category)
    secure_filename = handler.generate_secure_filename(filename, user_id)
    return handler.get_upload_path(secure_filename, user_id)
