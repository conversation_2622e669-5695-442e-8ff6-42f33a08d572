"""
Signal handlers for automatic analytics tracking
"""

from django.db.models.signals import post_save, post_delete
from django.contrib.auth.signals import user_logged_in, user_logged_out
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone
from decimal import Decimal

from .models import UserActivity, BettingPattern, DailyMetrics, SportPopularity
from .utils import get_client_ip_from_request, update_user_segments

User = get_user_model()


@receiver(user_logged_in)
def track_user_login(sender, request, user, **kwargs):
    """Track user login activity"""
    UserActivity.objects.create(
        user=user,
        session_id=request.session.session_key,
        action_type='login',
        ip_address=get_client_ip_from_request(request),
        user_agent=request.META.get('HTTP_USER_AGENT', ''),
        metadata={
            'login_method': 'standard',
            'device_type': _detect_device_type(request.META.get('HTTP_USER_AGENT', ''))
        }
    )


@receiver(user_logged_out)
def track_user_logout(sender, request, user, **kwargs):
    """Track user logout activity"""
    if user:
        UserActivity.objects.create(
            user=user,
            session_id=request.session.session_key if request.session.session_key else None,
            action_type='logout',
            ip_address=get_client_ip_from_request(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )


@receiver(post_save, sender=User)
def track_user_registration(sender, instance, created, **kwargs):
    """Track new user registrations"""
    if created:
        UserActivity.objects.create(
            user=instance,
            action_type='register',
            metadata={
                'registration_date': instance.date_joined.isoformat(),
                'verification_required': not instance.is_verified if hasattr(instance, 'is_verified') else True
            }
        )
        
        # Update daily metrics
        _update_daily_metrics_for_registration()


def track_bet_placed(user, bet_instance, request=None):
    """Track when a bet is placed"""
    metadata = {
        'bet_id': bet_instance.id,
        'bet_type': bet_instance.bet_type,
        'stake': str(bet_instance.stake),
        'potential_winnings': str(bet_instance.potential_winnings),
        'odds_count': bet_instance.selections.count() if hasattr(bet_instance, 'selections') else 1,
    }
    
    # Add sport information if available
    if hasattr(bet_instance, 'selections') and bet_instance.selections.exists():
        first_selection = bet_instance.selections.first()
        if hasattr(first_selection, 'market') and hasattr(first_selection.market, 'event'):
            event = first_selection.market.event
            metadata.update({
                'sport': event.sport.name if hasattr(event, 'sport') else 'Unknown',
                'league': event.league if hasattr(event, 'league') else None,
                'event_name': str(event),
            })
    
    UserActivity.objects.create(
        user=user,
        session_id=request.session.session_key if request and request.session.session_key else None,
        action_type='bet_placed',
        ip_address=get_client_ip_from_request(request) if request else None,
        user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
        metadata=metadata
    )
    
    # Update betting patterns
    _update_betting_patterns(user, bet_instance)
    
    # Update daily metrics
    _update_daily_metrics_for_bet(bet_instance)


def track_bet_result(user, bet_instance, result):
    """Track bet results (won/lost)"""
    action_type = 'bet_won' if result == 'won' else 'bet_lost'
    
    metadata = {
        'bet_id': bet_instance.id,
        'result': result,
        'stake': str(bet_instance.stake),
        'winnings': str(bet_instance.winnings) if hasattr(bet_instance, 'winnings') and bet_instance.winnings else '0.00',
    }
    
    UserActivity.objects.create(
        user=user,
        action_type=action_type,
        metadata=metadata
    )
    
    # Update betting patterns with result
    _update_betting_patterns_result(user, bet_instance, result)


def track_deposit(user, transaction, request=None):
    """Track deposit transactions"""
    UserActivity.objects.create(
        user=user,
        session_id=request.session.session_key if request and request.session.session_key else None,
        action_type='deposit',
        ip_address=get_client_ip_from_request(request) if request else None,
        metadata={
            'transaction_id': transaction.id,
            'amount': str(transaction.amount),
            'payment_method': transaction.payment_method,
            'status': transaction.status,
        }
    )
    
    # Update daily metrics
    _update_daily_metrics_for_deposit(transaction)


def track_withdrawal(user, transaction, request=None):
    """Track withdrawal transactions"""
    UserActivity.objects.create(
        user=user,
        session_id=request.session.session_key if request and request.session.session_key else None,
        action_type='withdrawal',
        ip_address=get_client_ip_from_request(request) if request else None,
        metadata={
            'transaction_id': transaction.id,
            'amount': str(transaction.amount),
            'payment_method': transaction.payment_method,
            'status': transaction.status,
        }
    )
    
    # Update daily metrics
    _update_daily_metrics_for_withdrawal(transaction)


def track_page_view(request, page_name=None):
    """Track page views"""
    user = request.user if request.user.is_authenticated else None
    
    UserActivity.objects.create(
        user=user,
        session_id=request.session.session_key,
        action_type='page_view',
        ip_address=get_client_ip_from_request(request),
        user_agent=request.META.get('HTTP_USER_AGENT', ''),
        page_url=request.build_absolute_uri(),
        referrer=request.META.get('HTTP_REFERER'),
        metadata={
            'page_name': page_name or request.resolver_match.url_name if hasattr(request, 'resolver_match') else 'unknown',
            'method': request.method,
        }
    )


def _detect_device_type(user_agent):
    """Detect device type from user agent"""
    user_agent_lower = user_agent.lower()
    
    if any(mobile in user_agent_lower for mobile in ['mobile', 'android', 'iphone', 'ipad']):
        return 'mobile'
    elif 'tablet' in user_agent_lower:
        return 'tablet'
    else:
        return 'desktop'


def _update_daily_metrics_for_registration():
    """Update daily metrics for new registration"""
    today = timezone.now().date()
    metrics, created = DailyMetrics.objects.get_or_create(date=today)
    metrics.new_registrations += 1
    metrics.save(update_fields=['new_registrations', 'updated_at'])


def _update_daily_metrics_for_bet(bet_instance):
    """Update daily metrics for bet placement"""
    today = timezone.now().date()
    metrics, created = DailyMetrics.objects.get_or_create(date=today)
    
    metrics.total_bets_placed += 1
    metrics.total_bet_amount += bet_instance.stake
    metrics.save(update_fields=['total_bets_placed', 'total_bet_amount', 'updated_at'])


def _update_daily_metrics_for_deposit(transaction):
    """Update daily metrics for deposit"""
    today = timezone.now().date()
    metrics, created = DailyMetrics.objects.get_or_create(date=today)
    
    if transaction.status == 'completed':
        metrics.total_deposits += transaction.amount
        metrics.save(update_fields=['total_deposits', 'updated_at'])


def _update_daily_metrics_for_withdrawal(transaction):
    """Update daily metrics for withdrawal"""
    today = timezone.now().date()
    metrics, created = DailyMetrics.objects.get_or_create(date=today)
    
    if transaction.status == 'completed':
        metrics.total_withdrawals += transaction.amount
        metrics.save(update_fields=['total_withdrawals', 'updated_at'])


def _update_betting_patterns(user, bet_instance):
    """Update user betting patterns"""
    # Get sport information
    sport = 'Unknown'
    league = None
    
    if hasattr(bet_instance, 'selections') and bet_instance.selections.exists():
        first_selection = bet_instance.selections.first()
        if hasattr(first_selection, 'market') and hasattr(first_selection.market, 'event'):
            event = first_selection.market.event
            sport = event.sport.name if hasattr(event, 'sport') else 'Unknown'
            league = event.league if hasattr(event, 'league') else None
    
    # Update or create betting pattern
    pattern, created = BettingPattern.objects.get_or_create(
        user=user,
        sport=sport,
        bet_type=bet_instance.bet_type,
        defaults={
            'league': league,
            'total_bets': 0,
            'total_amount_bet': Decimal('0.00'),
            'total_winnings': Decimal('0.00'),
            'average_stake': Decimal('0.00'),
        }
    )
    
    # Update statistics
    pattern.total_bets += 1
    pattern.total_amount_bet += bet_instance.stake
    
    # Recalculate average stake
    pattern.average_stake = pattern.total_amount_bet / pattern.total_bets
    
    pattern.save()


def _update_betting_patterns_result(user, bet_instance, result):
    """Update betting patterns with bet result"""
    sport = 'Unknown'
    
    if hasattr(bet_instance, 'selections') and bet_instance.selections.exists():
        first_selection = bet_instance.selections.first()
        if hasattr(first_selection, 'market') and hasattr(first_selection.market, 'event'):
            event = first_selection.market.event
            sport = event.sport.name if hasattr(event, 'sport') else 'Unknown'
    
    try:
        pattern = BettingPattern.objects.get(
            user=user,
            sport=sport,
            bet_type=bet_instance.bet_type
        )
        
        if result == 'won' and hasattr(bet_instance, 'winnings') and bet_instance.winnings:
            pattern.total_winnings += bet_instance.winnings
        
        # Recalculate win rate
        won_bets = UserActivity.objects.filter(
            user=user,
            action_type='bet_won',
            metadata__bet_id=bet_instance.id
        ).count()
        
        if pattern.total_bets > 0:
            pattern.win_rate = (won_bets / pattern.total_bets) * 100
        
        pattern.save()
        
    except BettingPattern.DoesNotExist:
        pass  # Pattern might not exist yet
