"""
Analytics models for tracking user behavior and platform metrics
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import json

User = get_user_model()


class UserActivity(models.Model):
    """
    Track user activities and interactions on the platform
    """
    ACTION_TYPES = [
        ('login', 'User Login'),
        ('logout', 'User Logout'),
        ('register', 'User Registration'),
        ('bet_placed', 'Bet Placed'),
        ('bet_won', 'Bet Won'),
        ('bet_lost', 'Bet Lost'),
        ('deposit', 'Money Deposited'),
        ('withdrawal', 'Money Withdrawn'),
        ('profile_update', 'Profile Updated'),
        ('password_change', 'Password Changed'),
        ('support_ticket', 'Support Ticket Created'),
        ('page_view', 'Page View'),
        ('search', 'Search Performed'),
        ('odds_view', 'Odds Viewed'),
        ('live_bet', 'Live Bet Placed'),
        ('jackpot_entry', 'Jackpot Entry'),
    ]
    
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='activities',
        null=True, 
        blank=True  # Allow anonymous activities
    )
    session_id = models.CharField(max_length=40, null=True, blank=True)
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    timestamp = models.DateTimeField(default=timezone.now, db_index=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(null=True, blank=True)
    page_url = models.URLField(null=True, blank=True)
    referrer = models.URLField(null=True, blank=True)
    
    # Additional context data stored as JSON
    metadata = models.JSONField(default=dict, blank=True)
    
    # Performance metrics
    response_time = models.FloatField(null=True, blank=True, help_text="Response time in seconds")
    
    class Meta:
        db_table = 'analytics_user_activity'
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['action_type', 'timestamp']),
            models.Index(fields=['timestamp']),
            models.Index(fields=['session_id']),
        ]
        ordering = ['-timestamp']
    
    def __str__(self):
        user_str = str(self.user) if self.user else 'Anonymous'
        return f"{user_str} - {self.get_action_type_display()} at {self.timestamp}"


class BettingPattern(models.Model):
    """
    Track betting patterns and preferences for users
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='betting_patterns')
    sport = models.CharField(max_length=50, db_index=True)
    league = models.CharField(max_length=100, null=True, blank=True)
    bet_type = models.CharField(max_length=20, db_index=True)  # single, multi, system
    
    # Betting statistics
    total_bets = models.PositiveIntegerField(default=0)
    total_amount_bet = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_winnings = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    win_rate = models.FloatField(default=0.0, validators=[MinValueValidator(0.0), MaxValueValidator(100.0)])
    
    # Preferences
    average_stake = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    preferred_odds_range_min = models.FloatField(default=1.0)
    preferred_odds_range_max = models.FloatField(default=10.0)
    
    # Time patterns
    most_active_hour = models.PositiveSmallIntegerField(null=True, blank=True)
    most_active_day = models.PositiveSmallIntegerField(null=True, blank=True)  # 0=Monday
    
    # Tracking
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'analytics_betting_pattern'
        unique_together = ['user', 'sport', 'bet_type']
        indexes = [
            models.Index(fields=['user', 'sport']),
            models.Index(fields=['sport', 'bet_type']),
            models.Index(fields=['win_rate']),
        ]
    
    def __str__(self):
        return f"{self.user} - {self.sport} {self.bet_type} betting pattern"


class DailyMetrics(models.Model):
    """
    Daily aggregated metrics for the platform
    """
    date = models.DateField(unique=True, db_index=True)
    
    # User metrics
    new_registrations = models.PositiveIntegerField(default=0)
    active_users = models.PositiveIntegerField(default=0)
    returning_users = models.PositiveIntegerField(default=0)
    
    # Betting metrics
    total_bets_placed = models.PositiveIntegerField(default=0)
    total_bet_amount = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    total_winnings_paid = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    
    # Financial metrics
    total_deposits = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    total_withdrawals = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    gross_gaming_revenue = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    
    # Platform metrics
    page_views = models.PositiveIntegerField(default=0)
    unique_visitors = models.PositiveIntegerField(default=0)
    bounce_rate = models.FloatField(default=0.0)
    average_session_duration = models.FloatField(default=0.0)  # in minutes
    
    # Support metrics
    support_tickets_created = models.PositiveIntegerField(default=0)
    support_tickets_resolved = models.PositiveIntegerField(default=0)
    
    # System metrics
    average_response_time = models.FloatField(default=0.0)  # in seconds
    error_rate = models.FloatField(default=0.0)  # percentage
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'analytics_daily_metrics'
        ordering = ['-date']
    
    def __str__(self):
        return f"Daily metrics for {self.date}"
    
    @property
    def profit_margin(self):
        """Calculate profit margin percentage"""
        if self.total_bet_amount > 0:
            return float((self.gross_gaming_revenue / self.total_bet_amount) * 100)
        return 0.0


class SportPopularity(models.Model):
    """
    Track popularity of different sports and leagues
    """
    sport = models.CharField(max_length=50, db_index=True)
    league = models.CharField(max_length=100, null=True, blank=True)
    
    # Popularity metrics
    total_bets = models.PositiveIntegerField(default=0)
    total_amount_bet = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    unique_bettors = models.PositiveIntegerField(default=0)
    
    # Engagement metrics
    odds_views = models.PositiveIntegerField(default=0)
    live_bets = models.PositiveIntegerField(default=0)
    
    # Time tracking
    date = models.DateField(default=timezone.now, db_index=True)
    last_updated = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'analytics_sport_popularity'
        unique_together = ['sport', 'league', 'date']
        indexes = [
            models.Index(fields=['sport', 'date']),
            models.Index(fields=['total_bets']),
            models.Index(fields=['total_amount_bet']),
        ]
    
    def __str__(self):
        league_str = f" - {self.league}" if self.league else ""
        return f"{self.sport}{league_str} on {self.date}"


class UserSegment(models.Model):
    """
    User segmentation for marketing and analytics
    """
    SEGMENT_TYPES = [
        ('high_value', 'High Value Customer'),
        ('frequent_bettor', 'Frequent Bettor'),
        ('casual_bettor', 'Casual Bettor'),
        ('new_user', 'New User'),
        ('inactive', 'Inactive User'),
        ('at_risk', 'At Risk User'),
        ('vip', 'VIP Customer'),
        ('problem_gambler', 'Problem Gambler'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='segments')
    segment_type = models.CharField(max_length=20, choices=SEGMENT_TYPES)
    
    # Segment criteria
    criteria = models.JSONField(default=dict, help_text="Criteria used to assign this segment")
    confidence_score = models.FloatField(
        default=1.0, 
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        help_text="Confidence in segment assignment (0-1)"
    )
    
    # Tracking
    assigned_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'analytics_user_segment'
        unique_together = ['user', 'segment_type']
        indexes = [
            models.Index(fields=['segment_type', 'is_active']),
            models.Index(fields=['user', 'is_active']),
        ]
    
    def __str__(self):
        return f"{self.user} - {self.get_segment_type_display()}"


class RevenueReport(models.Model):
    """
    Revenue and financial reporting
    """
    REPORT_TYPES = [
        ('daily', 'Daily Report'),
        ('weekly', 'Weekly Report'),
        ('monthly', 'Monthly Report'),
        ('quarterly', 'Quarterly Report'),
        ('yearly', 'Yearly Report'),
    ]
    
    report_type = models.CharField(max_length=10, choices=REPORT_TYPES)
    period_start = models.DateTimeField(db_index=True)
    period_end = models.DateTimeField(db_index=True)
    
    # Revenue metrics
    gross_gaming_revenue = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    net_gaming_revenue = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    total_deposits = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    total_withdrawals = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    
    # Cost metrics
    payment_processing_fees = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    operational_costs = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    marketing_costs = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    
    # Calculated metrics
    profit_before_tax = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    profit_margin = models.FloatField(default=0.0)
    
    # Additional data
    metadata = models.JSONField(default=dict, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'analytics_revenue_report'
        unique_together = ['report_type', 'period_start', 'period_end']
        ordering = ['-period_start']
    
    def __str__(self):
        return f"{self.get_report_type_display()} - {self.period_start.date()} to {self.period_end.date()}"
