"""
Management command to generate automated reports
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from datetime import datetime, timedelta
import json
import csv
import io

from django.db import models
from analytics.models import DailyMetrics, RevenueReport, UserActivity, BettingPattern
from analytics.utils import (
    calculate_daily_metrics, generate_revenue_report, 
    get_platform_analytics_summary, update_user_segments
)


class Command(BaseCommand):
    help = 'Generate automated analytics reports'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['daily', 'weekly', 'monthly', 'all'],
            default='daily',
            help='Type of report to generate'
        )
        
        parser.add_argument(
            '--email',
            action='store_true',
            help='Send reports via email'
        )
        
        parser.add_argument(
            '--date',
            type=str,
            help='Specific date for report (YYYY-MM-DD format)'
        )
        
        parser.add_argument(
            '--recipients',
            type=str,
            nargs='+',
            help='Email recipients for reports'
        )
        
        parser.add_argument(
            '--format',
            type=str,
            choices=['json', 'csv', 'html'],
            default='json',
            help='Report output format'
        )
    
    def handle(self, *args, **options):
        report_type = options['type']
        send_email = options['email']
        report_date = options.get('date')
        recipients = options.get('recipients', [])
        output_format = options['format']
        
        self.stdout.write(
            self.style.SUCCESS(f'Generating {report_type} analytics reports...')
        )
        
        try:
            if report_type == 'daily' or report_type == 'all':
                self._generate_daily_report(report_date, send_email, recipients, output_format)
            
            if report_type == 'weekly' or report_type == 'all':
                self._generate_weekly_report(send_email, recipients, output_format)
            
            if report_type == 'monthly' or report_type == 'all':
                self._generate_monthly_report(send_email, recipients, output_format)
            
            # Update user segments
            self.stdout.write('Updating user segments...')
            update_user_segments()
            
            self.stdout.write(
                self.style.SUCCESS('Reports generated successfully!')
            )
            
        except Exception as e:
            raise CommandError(f'Error generating reports: {str(e)}')
    
    def _generate_daily_report(self, report_date, send_email, recipients, output_format):
        """Generate daily analytics report"""
        if report_date:
            date = datetime.strptime(report_date, '%Y-%m-%d').date()
        else:
            date = timezone.now().date() - timedelta(days=1)  # Yesterday
        
        self.stdout.write(f'Generating daily report for {date}...')
        
        # Calculate daily metrics
        metrics = calculate_daily_metrics(date)
        
        # Get additional analytics
        platform_summary = get_platform_analytics_summary(1)  # Last 1 day
        
        # Generate revenue report
        revenue_report = generate_revenue_report('daily', 
            datetime.combine(date, datetime.min.time()),
            datetime.combine(date, datetime.max.time())
        )
        
        report_data = {
            'report_type': 'daily',
            'date': str(date),
            'metrics': {
                'new_registrations': metrics.new_registrations,
                'active_users': metrics.active_users,
                'total_bets_placed': metrics.total_bets_placed,
                'total_bet_amount': float(metrics.total_bet_amount),
                'total_deposits': float(metrics.total_deposits),
                'total_withdrawals': float(metrics.total_withdrawals),
                'page_views': metrics.page_views,
                'unique_visitors': metrics.unique_visitors,
                'bounce_rate': metrics.bounce_rate,
            },
            'revenue': {
                'gross_gaming_revenue': float(revenue_report.gross_gaming_revenue),
                'net_gaming_revenue': float(revenue_report.net_gaming_revenue),
                'profit_margin': revenue_report.profit_margin,
            },
            'generated_at': timezone.now().isoformat()
        }
        
        # Save report
        self._save_report(f'daily_report_{date}', report_data, output_format)
        
        # Send email if requested
        if send_email and recipients:
            self._send_email_report('Daily Analytics Report', report_data, recipients)
    
    def _generate_weekly_report(self, send_email, recipients, output_format):
        """Generate weekly analytics report"""
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=7)
        
        self.stdout.write(f'Generating weekly report for {start_date} to {end_date}...')
        
        # Get weekly metrics
        weekly_metrics = DailyMetrics.objects.filter(
            date__range=(start_date, end_date)
        )
        
        # Calculate totals
        total_registrations = sum(m.new_registrations for m in weekly_metrics)
        total_bets = sum(m.total_bets_placed for m in weekly_metrics)
        total_revenue = sum(m.total_bet_amount for m in weekly_metrics)
        avg_active_users = sum(m.active_users for m in weekly_metrics) / len(weekly_metrics) if weekly_metrics else 0
        
        # Get platform summary
        platform_summary = get_platform_analytics_summary(7)
        
        report_data = {
            'report_type': 'weekly',
            'period': f'{start_date} to {end_date}',
            'summary': {
                'total_registrations': total_registrations,
                'total_bets': total_bets,
                'total_revenue': float(total_revenue),
                'avg_active_users': round(avg_active_users, 2),
            },
            'daily_breakdown': [
                {
                    'date': str(m.date),
                    'active_users': m.active_users,
                    'bets': m.total_bets_placed,
                    'revenue': float(m.total_bet_amount)
                }
                for m in weekly_metrics
            ],
            'growth_metrics': platform_summary.get('growth_metrics', {}),
            'generated_at': timezone.now().isoformat()
        }
        
        # Save report
        self._save_report(f'weekly_report_{start_date}_to_{end_date}', report_data, output_format)
        
        # Send email if requested
        if send_email and recipients:
            self._send_email_report('Weekly Analytics Report', report_data, recipients)
    
    def _generate_monthly_report(self, send_email, recipients, output_format):
        """Generate monthly analytics report"""
        today = timezone.now().date()
        start_date = today.replace(day=1)  # First day of current month
        
        self.stdout.write(f'Generating monthly report for {start_date.strftime("%B %Y")}...')
        
        # Get monthly metrics
        monthly_metrics = DailyMetrics.objects.filter(
            date__gte=start_date,
            date__lt=today
        )
        
        # Calculate totals
        total_registrations = sum(m.new_registrations for m in monthly_metrics)
        total_bets = sum(m.total_bets_placed for m in monthly_metrics)
        total_revenue = sum(m.total_bet_amount for m in monthly_metrics)
        total_deposits = sum(m.total_deposits for m in monthly_metrics)
        total_withdrawals = sum(m.total_withdrawals for m in monthly_metrics)
        
        # Get top betting patterns
        top_sports = BettingPattern.objects.values('sport').annotate(
            total_bets=models.Sum('total_bets'),
            total_amount=models.Sum('total_amount_bet')
        ).order_by('-total_amount')[:5]
        
        # Generate revenue report
        revenue_report = generate_revenue_report('monthly', 
            datetime.combine(start_date, datetime.min.time()),
            datetime.combine(today, datetime.max.time())
        )
        
        report_data = {
            'report_type': 'monthly',
            'period': start_date.strftime('%B %Y'),
            'summary': {
                'total_registrations': total_registrations,
                'total_bets': total_bets,
                'total_revenue': float(total_revenue),
                'total_deposits': float(total_deposits),
                'total_withdrawals': float(total_withdrawals),
                'net_cash_flow': float(total_deposits - total_withdrawals),
            },
            'revenue_analysis': {
                'gross_gaming_revenue': float(revenue_report.gross_gaming_revenue),
                'net_gaming_revenue': float(revenue_report.net_gaming_revenue),
                'profit_margin': revenue_report.profit_margin,
            },
            'top_sports': [
                {
                    'sport': sport['sport'],
                    'total_bets': sport['total_bets'],
                    'total_amount': float(sport['total_amount'])
                }
                for sport in top_sports
            ],
            'generated_at': timezone.now().isoformat()
        }
        
        # Save report
        self._save_report(f'monthly_report_{start_date.strftime("%Y_%m")}', report_data, output_format)
        
        # Send email if requested
        if send_email and recipients:
            self._send_email_report('Monthly Analytics Report', report_data, recipients)
    
    def _save_report(self, filename, data, output_format):
        """Save report to file"""
        import os
        
        # Create reports directory if it doesn't exist
        reports_dir = os.path.join(settings.BASE_DIR, 'reports')
        os.makedirs(reports_dir, exist_ok=True)
        
        if output_format == 'json':
            filepath = os.path.join(reports_dir, f'{filename}.json')
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2, default=str)
        
        elif output_format == 'csv':
            filepath = os.path.join(reports_dir, f'{filename}.csv')
            with open(filepath, 'w', newline='') as f:
                if 'daily_breakdown' in data:
                    writer = csv.DictWriter(f, fieldnames=['date', 'active_users', 'bets', 'revenue'])
                    writer.writeheader()
                    writer.writerows(data['daily_breakdown'])
                else:
                    # Flatten the data for CSV
                    flattened = self._flatten_dict(data)
                    writer = csv.DictWriter(f, fieldnames=flattened.keys())
                    writer.writeheader()
                    writer.writerow(flattened)
        
        elif output_format == 'html':
            filepath = os.path.join(reports_dir, f'{filename}.html')
            html_content = self._generate_html_report(data)
            with open(filepath, 'w') as f:
                f.write(html_content)
        
        self.stdout.write(f'Report saved to: {filepath}')
    
    def _flatten_dict(self, d, parent_key='', sep='_'):
        """Flatten nested dictionary for CSV export"""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)
    
    def _generate_html_report(self, data):
        """Generate HTML report"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Analytics Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; }
                .metric { display: inline-block; margin: 10px; padding: 15px; background-color: #e9ecef; border-radius: 5px; }
                .metric h3 { margin: 0; color: #495057; }
                .metric p { margin: 5px 0 0 0; font-size: 24px; font-weight: bold; color: #007bff; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #dee2e6; padding: 8px; text-align: left; }
                th { background-color: #f8f9fa; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{report_type} Analytics Report</h1>
                <p>Generated on: {generated_at}</p>
            </div>
            
            <div class="metrics">
                <!-- Metrics will be inserted here -->
            </div>
            
            <div class="data">
                <pre>{data}</pre>
            </div>
        </body>
        </html>
        """.format(
            report_type=data.get('report_type', 'Analytics').title(),
            generated_at=data.get('generated_at', ''),
            data=json.dumps(data, indent=2, default=str)
        )
        
        return html_template
    
    def _send_email_report(self, subject, data, recipients):
        """Send report via email"""
        try:
            message = f"""
            Analytics Report Generated
            
            Report Type: {data.get('report_type', 'Unknown')}
            Generated At: {data.get('generated_at', 'Unknown')}
            
            Summary:
            {json.dumps(data.get('summary', {}), indent=2, default=str)}
            
            Full report data is attached or available in the system.
            """
            
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                recipients,
                fail_silently=False,
            )
            
            self.stdout.write(f'Email sent to: {", ".join(recipients)}')
            
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'Failed to send email: {str(e)}')
            )
