# Analytics and Reporting System

## Overview

The Analytics and Reporting System is a comprehensive solution for tracking user behavior, financial performance, and business metrics for the Betika Clone betting platform. It provides real-time insights, automated reporting, and data export capabilities.

## Features

### 📊 User Behavior Tracking
- **User Activities**: Track all user interactions (login, betting, deposits, withdrawals)
- **Betting Patterns**: Analyze user betting preferences and performance
- **User Segmentation**: Automatic categorization of users (high-value, frequent bettors, etc.)
- **Session Analytics**: Track user sessions and engagement metrics

### 💰 Financial Reporting
- **Transaction Reports**: Detailed transaction analysis with filtering
- **Profit & Loss Dashboard**: Comprehensive P&L statements with cost breakdowns
- **Revenue Analysis**: Gaming revenue tracking and sport-wise performance
- **Reconciliation Tools**: Automated payment reconciliation and discrepancy detection

### 📈 Business Intelligence
- **Daily Metrics**: Automated calculation of key daily performance indicators
- **Platform Analytics**: Overall platform performance and growth metrics
- **Sport Popularity**: Track which sports and leagues are most popular
- **Revenue Reports**: Automated revenue reporting (daily, weekly, monthly)

### 📤 Data Export
- **Multiple Formats**: Export data in JSON, CSV, and Excel formats
- **Flexible Filtering**: Filter exports by date range, user, sport, etc.
- **Automated Reports**: Schedule automated report generation
- **API Endpoints**: RESTful APIs for data access

## Architecture

### Models
- **UserActivity**: Tracks all user interactions
- **BettingPattern**: Stores user betting preferences and statistics
- **DailyMetrics**: Daily aggregated platform metrics
- **SportPopularity**: Sport and league popularity tracking
- **UserSegment**: User categorization and segmentation
- **RevenueReport**: Financial reporting data

### Key Components
- **Analytics Utils**: Core calculation and aggregation functions
- **Financial Reports**: Transaction and revenue analysis
- **Profit & Loss Calculator**: Comprehensive P&L calculations
- **Data Exporters**: Multi-format data export system
- **Signal Handlers**: Automatic activity tracking

## Installation

1. The analytics app is already included in `INSTALLED_APPS`
2. Run migrations:
   ```bash
   python manage.py migrate analytics
   ```

## Usage

### Accessing Dashboards

#### Main Analytics Dashboard
```
/analytics/
```
- Overview of key metrics
- Daily activity trends
- Sport popularity charts
- User segment distribution

#### Financial Reports
```
/analytics/financial/
```
- Revenue reports by period
- Transaction summaries
- Payment method performance

#### Transaction Reports
```
/analytics/transactions/
```
- Detailed transaction analysis
- User transaction patterns
- Daily transaction trends

#### Profit & Loss Dashboard
```
/analytics/profit-loss/
```
- Comprehensive P&L statements
- Cost breakdowns
- Monthly comparisons
- Key performance indicators

#### Reconciliation Dashboard
```
/analytics/reconciliation/
```
- Payment reconciliation status
- Discrepancy detection
- Automated reconciliation tools

### Management Commands

#### Update Daily Metrics
```bash
python manage.py update_daily_metrics --days=7
```

#### Generate Reports
```bash
python manage.py generate_reports --type=daily --email --recipients=<EMAIL>
```

#### Run Reconciliation
```bash
python manage.py run_reconciliation --days=1 --fix-discrepancies
```

### API Endpoints

#### Get Analytics Data
```
GET /analytics/api/data/?type=daily_metrics&days=30
```

#### Export Data
```
GET /analytics/api/export/?type=user_activities&format=json&days=7
```

## Data Export

### Available Export Types
- `user_activities`: User interaction data
- `betting_patterns`: User betting preferences
- `daily_metrics`: Daily platform metrics
- `revenue_reports`: Financial reports
- `sport_popularity`: Sport popularity data

### Export Formats
- **JSON**: Structured data with metadata
- **CSV**: Tabular data for spreadsheet analysis
- **Excel**: Formatted spreadsheets with multiple sheets

### Example Export
```python
from analytics.exporters import AnalyticsExporter
from datetime import datetime, timedelta

end_date = datetime.now()
start_date = end_date - timedelta(days=30)

# Export user activities as JSON
response = AnalyticsExporter.export_user_activities(
    start_date, end_date, 'json'
)
```

## Automated Reporting

### Schedule Reports
Set up cron jobs or scheduled tasks to run automated reports:

```bash
# Daily reports at 6 AM
0 6 * * * /path/to/venv/bin/python /path/to/manage.py generate_reports --type=daily

# Weekly reports on Mondays
0 8 * * 1 /path/to/venv/bin/python /path/to/manage.py generate_reports --type=weekly --email

# Monthly reconciliation
0 9 1 * * /path/to/venv/bin/python /path/to/manage.py run_reconciliation --days=30
```

## Security and Permissions

### Access Control
- All analytics views require staff permissions
- Personal analytics available to authenticated users
- API endpoints protected with authentication
- Sensitive data is encrypted at rest

### Data Privacy
- User data is anonymized in exports when possible
- IP addresses and sensitive metadata are handled securely
- Compliance with data protection regulations

## Performance Considerations

### Database Optimization
- Indexed fields for fast queries
- Efficient aggregation queries
- Pagination for large datasets
- Background processing for heavy calculations

### Caching
- Daily metrics are cached for performance
- Report results are cached for repeated access
- API responses use appropriate cache headers

## Testing

Run the comprehensive test suite:

```bash
# Run all analytics tests
python manage.py test analytics

# Run specific test classes
python manage.py test analytics.tests.AnalyticsModelsTest
python manage.py test analytics.tests.FinancialReportsTest
python manage.py test analytics.tests.ExportersTest
```

## Monitoring and Alerts

### Key Metrics to Monitor
- Daily active users
- Transaction success rates
- Revenue trends
- System performance
- Data quality

### Alert Conditions
- Significant drops in user activity
- High transaction failure rates
- Unusual betting patterns
- System errors or performance issues

## Future Enhancements

### Planned Features
- Machine learning for user behavior prediction
- Advanced fraud detection
- Real-time streaming analytics
- Mobile app analytics integration
- A/B testing framework

### Integration Opportunities
- Business intelligence tools (Tableau, Power BI)
- Data warehousing solutions
- External analytics platforms
- Marketing automation tools

## Support

For technical support or feature requests, please contact the development team or create an issue in the project repository.

## License

This analytics system is part of the Betika Clone project and follows the same licensing terms.
