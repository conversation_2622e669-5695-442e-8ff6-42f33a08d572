"""
Support views for tickets, FAQ, and live chat
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
import json

from .models import (
    SupportTicket, TicketResponse, SupportCategory,
    FAQArticle, FAQCategory, ChatSession, ChatMessage,
    SupportNotification
)
from .services import SupportTicketService, FAQService, ChatService


# Support Ticket Views
@login_required
def support_home(request):
    """Support home page with categories and quick actions"""
    categories = SupportCategory.objects.filter(is_active=True).order_by('sort_order')
    faq_categories = FAQCategory.objects.filter(is_active=True).order_by('sort_order')

    # Get user's recent tickets
    recent_tickets = SupportTicket.objects.filter(
        user=request.user
    ).order_by('-created_at')[:5]

    # Get popular FAQ articles
    faq_service = FAQService()
    popular_faqs = faq_service.get_popular_articles(limit=5)

    context = {
        'categories': categories,
        'faq_categories': faq_categories,
        'recent_tickets': recent_tickets,
        'popular_faqs': popular_faqs,
        'page_title': 'Support Center'
    }

    return render(request, 'support/home.html', context)


@login_required
def create_ticket(request):
    """Create a new support ticket"""
    if request.method == 'POST':
        try:
            subject = request.POST.get('subject', '').strip()
            description = request.POST.get('description', '').strip()
            category_id = request.POST.get('category')
            priority = request.POST.get('priority', 'normal')

            if not subject or not description:
                messages.error(request, 'Subject and description are required.')
                return redirect('support:create_ticket')

            # Create ticket using service
            ticket_service = SupportTicketService()
            ticket = ticket_service.create_ticket(
                user=request.user,
                subject=subject,
                description=description,
                category_id=category_id,
                priority=priority
            )

            messages.success(request, f'Support ticket {ticket.ticket_number} created successfully!')
            return redirect('support:ticket_detail', ticket_id=ticket.id)

        except Exception as e:
            messages.error(request, f'Error creating ticket: {str(e)}')
            return redirect('support:create_ticket')

    # GET request - show form
    categories = SupportCategory.objects.filter(is_active=True).order_by('sort_order')

    context = {
        'categories': categories,
        'page_title': 'Create Support Ticket'
    }

    return render(request, 'support/create_ticket.html', context)


@login_required
def ticket_list(request):
    """List user's support tickets"""
    tickets = SupportTicket.objects.filter(user=request.user)

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        tickets = tickets.filter(status=status_filter)

    # Search
    search_query = request.GET.get('q')
    if search_query:
        ticket_service = SupportTicketService()
        tickets = ticket_service.search_tickets(search_query, user=request.user)
    else:
        tickets = tickets.order_by('-created_at')

    # Pagination
    paginator = Paginator(tickets, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'tickets': page_obj,
        'status_filter': status_filter,
        'search_query': search_query,
        'page_title': 'My Support Tickets'
    }

    return render(request, 'support/ticket_list.html', context)


@login_required
def ticket_detail(request, ticket_id):
    """Display ticket details and responses"""
    ticket = get_object_or_404(SupportTicket, id=ticket_id)

    # Check permissions
    if not request.user.is_staff and ticket.user != request.user:
        messages.error(request, 'You do not have permission to view this ticket.')
        return redirect('support:ticket_list')

    # Handle response submission
    if request.method == 'POST':
        message = request.POST.get('message', '').strip()
        attachment = request.FILES.get('attachment')

        if message:
            try:
                ticket_service = SupportTicketService()
                response = ticket_service.add_response(
                    ticket=ticket,
                    user=request.user,
                    message=message,
                    attachment=attachment
                )

                messages.success(request, 'Response added successfully!')
                return redirect('support:ticket_detail', ticket_id=ticket.id)

            except Exception as e:
                messages.error(request, f'Error adding response: {str(e)}')
        else:
            messages.error(request, 'Message is required.')

    # Get responses (exclude internal notes for non-staff)
    responses = TicketResponse.objects.filter(ticket=ticket)
    if not request.user.is_staff:
        responses = responses.filter(is_internal_note=False)

    responses = responses.order_by('created_at')

    context = {
        'ticket': ticket,
        'responses': responses,
        'can_respond': ticket.is_open,
        'page_title': f'Ticket #{ticket.ticket_number}'
    }

    return render(request, 'support/ticket_detail.html', context)


# FAQ Views
@login_required
def faq_home(request):
    """FAQ home page with categories"""
    categories = FAQCategory.objects.filter(is_active=True).order_by('sort_order')

    # Get popular articles
    faq_service = FAQService()
    popular_articles = faq_service.get_popular_articles(limit=10)

    context = {
        'categories': categories,
        'popular_articles': popular_articles,
        'page_title': 'Frequently Asked Questions'
    }

    return render(request, 'support/faq_home.html', context)


@login_required
def faq_category(request, category_id):
    """Display FAQ articles in a category"""
    category = get_object_or_404(FAQCategory, id=category_id, is_active=True)

    faq_service = FAQService()
    articles = faq_service.get_articles_by_category(category_id)

    context = {
        'category': category,
        'articles': articles,
        'page_title': f'FAQ - {category.name}'
    }

    return render(request, 'support/faq_category.html', context)


@login_required
def faq_search(request):
    """Search FAQ articles"""
    query = request.GET.get('q', '').strip()
    category_id = request.GET.get('category')

    articles = []
    if query:
        faq_service = FAQService()
        articles = faq_service.search_articles(query, category_id)

    categories = FAQCategory.objects.filter(is_active=True).order_by('sort_order')

    context = {
        'articles': articles,
        'categories': categories,
        'query': query,
        'selected_category': category_id,
        'page_title': 'Search FAQ'
    }

    return render(request, 'support/faq_search.html', context)


@login_required
def faq_detail(request, article_id):
    """Display FAQ article details"""
    article = get_object_or_404(FAQArticle, id=article_id, is_published=True)

    # Increment view count
    article.increment_view_count()

    # Get related articles
    related_articles = FAQArticle.objects.filter(
        category=article.category,
        is_published=True
    ).exclude(id=article.id).order_by('-view_count')[:5]

    context = {
        'article': article,
        'related_articles': related_articles,
        'page_title': article.question
    }

    return render(request, 'support/faq_detail.html', context)


# Live Chat Views
@login_required
def chat_home(request):
    """Live chat interface"""
    # Get or create active chat session
    active_session = ChatSession.objects.filter(
        user=request.user,
        status__in=['waiting', 'active']
    ).first()

    if not active_session:
        # Show chat start form
        context = {
            'page_title': 'Live Chat Support'
        }
        return render(request, 'support/chat_start.html', context)

    # Get chat messages
    messages_list = ChatMessage.objects.filter(session=active_session).order_by('created_at')

    context = {
        'session': active_session,
        'messages': messages_list,
        'page_title': f'Live Chat - {active_session.session_id}'
    }

    return render(request, 'support/chat_interface.html', context)


@login_required
@require_http_methods(["POST"])
def start_chat(request):
    """Start a new chat session"""
    try:
        subject = request.POST.get('subject', '').strip()

        chat_service = ChatService()
        session = chat_service.start_chat_session(
            user=request.user,
            subject=subject
        )

        return redirect('support:chat_home')

    except Exception as e:
        messages.error(request, f'Error starting chat: {str(e)}')
        return redirect('support:chat_home')


@login_required
def chat_history(request):
    """Display user's chat history"""
    chat_service = ChatService()
    sessions = chat_service.get_user_chat_history(request.user, limit=20)

    context = {
        'sessions': sessions,
        'page_title': 'Chat History'
    }

    return render(request, 'support/chat_history.html', context)


# Staff Views
@staff_member_required
def staff_dashboard(request):
    """Staff dashboard with ticket and chat overview"""
    ticket_service = SupportTicketService()
    chat_service = ChatService()

    # Get ticket statistics
    ticket_stats = ticket_service.get_ticket_statistics()

    # Get waiting chats
    waiting_chats = chat_service.get_waiting_chats()

    # Get agent's active chats
    active_chats = chat_service.get_agent_active_chats(request.user)

    # Get recent tickets
    recent_tickets = SupportTicket.objects.order_by('-created_at')[:10]

    context = {
        'ticket_stats': ticket_stats,
        'waiting_chats': waiting_chats,
        'active_chats': active_chats,
        'recent_tickets': recent_tickets,
        'page_title': 'Support Dashboard'
    }

    return render(request, 'support/staff_dashboard.html', context)


@staff_member_required
def staff_ticket_list(request):
    """Staff ticket management"""
    tickets = SupportTicket.objects.all()

    # Filters
    status_filter = request.GET.get('status')
    priority_filter = request.GET.get('priority')
    assigned_filter = request.GET.get('assigned')

    if status_filter:
        tickets = tickets.filter(status=status_filter)

    if priority_filter:
        tickets = tickets.filter(priority=priority_filter)

    if assigned_filter == 'me':
        tickets = tickets.filter(assigned_to=request.user)
    elif assigned_filter == 'unassigned':
        tickets = tickets.filter(assigned_to__isnull=True)

    # Search
    search_query = request.GET.get('q')
    if search_query:
        ticket_service = SupportTicketService()
        tickets = ticket_service.search_tickets(search_query)
    else:
        tickets = tickets.order_by('-created_at')

    # Pagination
    paginator = Paginator(tickets, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'tickets': page_obj,
        'status_filter': status_filter,
        'priority_filter': priority_filter,
        'assigned_filter': assigned_filter,
        'search_query': search_query,
        'page_title': 'Manage Tickets'
    }

    return render(request, 'support/staff_ticket_list.html', context)


# API Views
@login_required
@require_http_methods(["POST"])
def api_vote_faq(request, article_id):
    """API endpoint to vote on FAQ article"""
    try:
        data = json.loads(request.body)
        is_helpful = data.get('is_helpful', True)

        faq_service = FAQService()
        success = faq_service.vote_article(article_id, is_helpful)

        if success:
            return JsonResponse({'success': True, 'message': 'Vote recorded successfully'})
        else:
            return JsonResponse({'success': False, 'error': 'Failed to record vote. Article may not exist or be unavailable.'})

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'error': 'Invalid JSON data'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["POST"])
def api_send_chat_message(request):
    """API endpoint to send chat message"""
    try:
        data = json.loads(request.body)
        session_id = data.get('session_id')
        message = data.get('message', '').strip()

        if not session_id or not message:
            return JsonResponse({'success': False, 'error': 'Session ID and message required'})

        # Get session
        session = get_object_or_404(ChatSession, id=session_id)

        # Check permissions
        if session.user != request.user and session.agent != request.user:
            return JsonResponse({'success': False, 'error': 'Permission denied'})

        # Send message
        chat_service = ChatService()
        chat_message = chat_service.send_message(
            session=session,
            sender=request.user,
            content=message
        )

        return JsonResponse({
            'success': True,
            'message': {
                'id': str(chat_message.id),
                'content': chat_message.content,
                'sender': chat_message.sender.get_full_name() or chat_message.sender.username,
                'is_from_agent': chat_message.is_from_agent,
                'created_at': chat_message.created_at.isoformat()
            }
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["GET"])
def api_get_chat_messages(request, session_id):
    """API endpoint to get chat messages"""
    try:
        session = get_object_or_404(ChatSession, id=session_id)

        # Check permissions
        if session.user != request.user and session.agent != request.user:
            return JsonResponse({'success': False, 'error': 'Permission denied'})

        # Get messages
        messages_list = ChatMessage.objects.filter(session=session).order_by('created_at')

        messages_data = []
        for msg in messages_list:
            messages_data.append({
                'id': str(msg.id),
                'content': msg.content,
                'sender': msg.sender.get_full_name() or msg.sender.username,
                'is_from_agent': msg.is_from_agent,
                'is_system_message': msg.is_system_message,
                'created_at': msg.created_at.isoformat()
            })

        return JsonResponse({
            'success': True,
            'messages': messages_data,
            'session_status': session.status
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@staff_member_required
@require_http_methods(["POST"])
def api_assign_ticket(request, ticket_id):
    """API endpoint to assign ticket to agent"""
    try:
        from typing import cast
        from accounts.models import CustomUser

        data = json.loads(request.body)
        agent_id = data.get('agent_id')

        ticket = get_object_or_404(SupportTicket, id=ticket_id)

        if agent_id:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            agent = cast(CustomUser, get_object_or_404(User, id=agent_id, is_staff=True))
        else:
            agent = cast(CustomUser, request.user)

        ticket_service = SupportTicketService()
        success = ticket_service.assign_ticket(ticket, agent, cast(CustomUser, request.user))

        if success:
            return JsonResponse({
                'success': True,
                'message': f'Ticket assigned to {agent.get_full_name() or agent.username}'
            })
        else:
            return JsonResponse({'success': False, 'error': 'Failed to assign ticket'})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@staff_member_required
@require_http_methods(["POST"])
def api_close_ticket(request, ticket_id):
    """API endpoint to close ticket"""
    try:
        data = json.loads(request.body)
        resolution = data.get('resolution', '')

        ticket = get_object_or_404(SupportTicket, id=ticket_id)

        ticket_service = SupportTicketService()
        success = ticket_service.close_ticket(ticket, request.user, resolution)

        if success:
            return JsonResponse({'success': True, 'message': 'Ticket closed successfully'})
        else:
            return JsonResponse({'success': False, 'error': 'Failed to close ticket'})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@staff_member_required
@require_http_methods(["POST"])
def api_join_chat(request, session_id):
    """API endpoint for agent to join chat"""
    try:
        session = get_object_or_404(ChatSession, id=session_id, status='waiting')

        chat_service = ChatService()
        success = chat_service.assign_agent_to_chat(session, request.user)

        if success:
            return JsonResponse({'success': True, 'message': 'Joined chat successfully'})
        else:
            return JsonResponse({'success': False, 'error': 'Failed to join chat'})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})
