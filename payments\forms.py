"""
Forms for payment functionality
"""

from django import forms
from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
from django.db import models
from decimal import Decimal
from .models import PaymentMethod, Transaction
from core.validators import InputSanitizer, InputValidator


class DepositForm(forms.Form):
    """
    Form for deposit functionality with multiple payment methods
    """
    PAYMENT_METHOD_CHOICES = [
        ('mpesa', 'M-Pesa'),
        ('card', 'Credit/Debit Card'),
        ('bank_transfer', 'Bank Transfer'),
    ]
    
    amount = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal('10.00'), message="Minimum deposit amount is KES 10.00"),
            MaxValueValidator(Decimal('500000.00'), message="Maximum deposit amount is KES 500,000.00")
        ],
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '0.00',
            'min': '10.00',
            'max': '500000.00',
            'step': '0.01'
        }),
        label='Deposit Amount (KES)'
    )
    
    payment_method = forms.ChoiceField(
        choices=PAYMENT_METHOD_CHOICES,
        widget=forms.RadioSelect(attrs={
            'class': 'payment-method-radio'
        }),
        label='Payment Method'
    )
    
    # M-Pesa specific fields
    mpesa_phone_number = forms.CharField(
        max_length=15,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+************',
            'pattern': r'^\+?254[0-9]{9}$'
        }),
        label='M-Pesa Phone Number'
    )
    
    # Card specific fields
    save_payment_method = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='Save this payment method for future use'
    )
    
    def clean_mpesa_phone_number(self):
        """Validate M-Pesa phone number format"""
        phone_number = self.cleaned_data.get('mpesa_phone_number')
        payment_method = self.cleaned_data.get('payment_method')

        if payment_method == 'mpesa':
            if not phone_number:
                raise ValidationError("M-Pesa phone number is required for M-Pesa payments")

            # Sanitize phone number
            sanitized_phone = InputSanitizer.sanitize_phone_number(phone_number)

            # Validate phone number format
            if not InputValidator.validate_phone_number(sanitized_phone):
                raise ValidationError("Please enter a valid phone number")

            # Additional validation for Kenyan M-Pesa numbers
            import re
            if not re.match(r'^\+254[17]\d{8}$', sanitized_phone):
                raise ValidationError("Please enter a valid Kenyan M-Pesa number")

            return sanitized_phone

        return phone_number

    def clean_amount(self):
        """Enhanced amount validation"""
        amount = self.cleaned_data.get('amount')

        if amount:
            # Use enhanced validation
            is_valid, error_msg = InputValidator.validate_betting_amount(amount)
            if not is_valid:
                raise ValidationError(error_msg)

            return InputSanitizer.sanitize_decimal(amount)

        return amount
    
    def clean(self):
        """Cross-field validation"""
        cleaned_data = super().clean()
        payment_method = cleaned_data.get('payment_method')
        amount = cleaned_data.get('amount')
        
        # Validate minimum amounts per payment method
        if payment_method == 'mpesa' and amount and amount < Decimal('10.00'):
            raise forms.ValidationError("Minimum M-Pesa deposit is KES 10.00")
        elif payment_method == 'card' and amount and amount < Decimal('100.00'):
            raise forms.ValidationError("Minimum card deposit is KES 100.00")
        elif payment_method == 'bank_transfer' and amount and amount < Decimal('500.00'):
            raise forms.ValidationError("Minimum bank transfer deposit is KES 500.00")
        
        return cleaned_data


class WithdrawalForm(forms.Form):
    """
    Form for withdrawal functionality with validation
    """
    PAYMENT_METHOD_CHOICES = [
        ('mpesa', 'M-Pesa'),
        ('bank_transfer', 'Bank Transfer'),
    ]
    
    amount = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[
            MinValueValidator(Decimal('50.00'), message="Minimum withdrawal amount is KES 50.00"),
            MaxValueValidator(Decimal('100000.00'), message="Maximum withdrawal amount is KES 100,000.00")
        ],
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': '0.00',
            'min': '50.00',
            'max': '100000.00',
            'step': '0.01'
        }),
        label='Withdrawal Amount (KES)'
    )
    
    payment_method = forms.ChoiceField(
        choices=PAYMENT_METHOD_CHOICES,
        widget=forms.RadioSelect(attrs={
            'class': 'payment-method-radio'
        }),
        label='Withdrawal Method'
    )
    
    # M-Pesa specific fields
    mpesa_phone_number = forms.CharField(
        max_length=15,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '+************',
            'pattern': r'^\+?254[0-9]{9}$'
        }),
        label='M-Pesa Phone Number'
    )
    
    # Bank transfer specific fields
    bank_name = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'e.g. Equity Bank'
        }),
        label='Bank Name'
    )
    
    account_number = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Account number'
        }),
        label='Account Number'
    )
    
    account_name = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Account holder name'
        }),
        label='Account Holder Name'
    )
    
    # Security confirmation
    confirm_withdrawal = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='I confirm the withdrawal details are correct and understand this action cannot be undone'
    )
    
    def __init__(self, user=None, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
    
    def clean_amount(self):
        """Validate withdrawal amount against user balance and daily limits"""
        amount = self.cleaned_data.get('amount')
        
        if not amount:
            return amount
        
        if self.user:
            # Check user balance
            if amount > self.user.balance:
                raise forms.ValidationError(
                    f"Insufficient balance. Your current balance is KES {self.user.balance}"
                )
            
            # Check daily withdrawal limit
            from django.utils import timezone
            from datetime import timedelta
            
            today = timezone.now().date()
            daily_withdrawals = Transaction.objects.filter(
                user=self.user,
                transaction_type='withdrawal',
                status__in=['pending', 'processing', 'completed'],
                created_at__date=today
            ).aggregate(total=models.Sum('amount'))['total'] or Decimal('0')
            
            daily_limit = Decimal('50000.00')  # KES 50,000 daily limit
            
            if daily_withdrawals + amount > daily_limit:
                remaining_limit = daily_limit - daily_withdrawals
                raise forms.ValidationError(
                    f"Daily withdrawal limit exceeded. You can withdraw up to KES {remaining_limit} more today."
                )
        
        return amount
    
    def clean_mpesa_phone_number(self):
        """Validate M-Pesa phone number format"""
        phone_number = self.cleaned_data.get('mpesa_phone_number')
        payment_method = self.cleaned_data.get('payment_method')
        
        if payment_method == 'mpesa':
            if not phone_number:
                raise forms.ValidationError("M-Pesa phone number is required for M-Pesa withdrawals")
            
            # Normalize phone number format
            if phone_number.startswith('0'):
                phone_number = '+254' + phone_number[1:]
            elif phone_number.startswith('254'):
                phone_number = '+' + phone_number
            elif not phone_number.startswith('+254'):
                raise forms.ValidationError("Please enter a valid Kenyan phone number")
            
            # Validate format
            if len(phone_number) != 13:
                raise forms.ValidationError("Please enter a valid phone number format: +************")
        
        return phone_number
    
    def clean(self):
        """Cross-field validation"""
        cleaned_data = super().clean()
        payment_method = cleaned_data.get('payment_method')
        amount = cleaned_data.get('amount')
        
        # Validate payment method specific fields
        if payment_method == 'mpesa':
            if not cleaned_data.get('mpesa_phone_number'):
                raise forms.ValidationError("M-Pesa phone number is required for M-Pesa withdrawals")
        
        elif payment_method == 'bank_transfer':
            required_fields = ['bank_name', 'account_number', 'account_name']
            for field in required_fields:
                if not cleaned_data.get(field):
                    raise forms.ValidationError(
                        f"{field.replace('_', ' ').title()} is required for bank transfer withdrawals"
                    )
        
        # Validate minimum amounts per payment method
        if amount:
            if payment_method == 'mpesa' and amount < Decimal('50.00'):
                raise forms.ValidationError("Minimum M-Pesa withdrawal is KES 50.00")
            elif payment_method == 'bank_transfer' and amount < Decimal('100.00'):
                raise forms.ValidationError("Minimum bank transfer withdrawal is KES 100.00")
        
        return cleaned_data


class AddPaymentMethodForm(forms.ModelForm):
    """
    Form for adding new payment methods
    """
    class Meta:
        model = PaymentMethod
        fields = ['payment_type', 'mpesa_phone_number', 'bank_name', 'account_number', 'account_name']
        widgets = {
            'payment_type': forms.Select(attrs={'class': 'form-control'}),
            'mpesa_phone_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '+************'
            }),
            'bank_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g. Equity Bank'
            }),
            'account_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Account number'
            }),
            'account_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Account holder name'
            }),
        }
    
    def clean_mpesa_phone_number(self):
        """Validate M-Pesa phone number"""
        phone_number = self.cleaned_data.get('mpesa_phone_number')
        payment_type = self.cleaned_data.get('payment_type')
        
        if payment_type == 'mpesa' and not phone_number:
            raise forms.ValidationError("M-Pesa phone number is required")
        
        if phone_number:
            # Normalize format
            if phone_number.startswith('0'):
                phone_number = '+254' + phone_number[1:]
            elif phone_number.startswith('254'):
                phone_number = '+' + phone_number
            
            if not phone_number.startswith('+254') or len(phone_number) != 13:
                raise forms.ValidationError("Please enter a valid Kenyan phone number")
        
        return phone_number
    
    def clean(self):
        """Cross-field validation"""
        cleaned_data = super().clean()
        payment_type = cleaned_data.get('payment_type')
        
        if payment_type == 'mpesa':
            if not cleaned_data.get('mpesa_phone_number'):
                raise forms.ValidationError("M-Pesa phone number is required for M-Pesa payment method")
        
        elif payment_type == 'bank_account':
            required_fields = ['bank_name', 'account_number', 'account_name']
            for field in required_fields:
                if not cleaned_data.get(field):
                    raise forms.ValidationError(f"{field.replace('_', ' ').title()} is required for bank account")
        
        return cleaned_data


class DepositConfirmationForm(forms.Form):
    """
    Form for confirming deposit details
    """
    transaction_id = forms.UUIDField(widget=forms.HiddenInput())
    confirm_deposit = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        label='I confirm the deposit details are correct'
    )