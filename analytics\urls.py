"""
URL configuration for analytics app
"""

from django.urls import path
from . import views

app_name = 'analytics'

urlpatterns = [
    # Dashboard
    path('', views.analytics_dashboard, name='dashboard'),
    path('dashboard/', views.analytics_dashboard, name='analytics_dashboard'),
    
    # User Analytics
    path('users/', views.user_analytics, name='user_analytics'),
    path('users/<int:user_id>/', views.user_analytics, name='user_detail_analytics'),
    
    # Betting Analytics
    path('betting/', views.betting_analytics, name='betting_analytics'),
    
    # Financial Reports
    path('financial/', views.financial_reports, name='financial_reports'),
    
    # Personal Analytics (for regular users)
    path('personal/', views.user_personal_analytics, name='personal_analytics'),
    
    # API Endpoints
    path('api/data/', views.api_analytics_data, name='api_data'),
    path('api/export/', views.export_data, name='export_data'),
]
