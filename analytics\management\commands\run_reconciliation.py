"""
Management command for automated payment reconciliation
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.db.models import Q, Sum, Count, Min
from datetime import datetime, timedelta
from decimal import Decimal
import json

from analytics.models import UserActivity


class Command(BaseCommand):
    help = 'Run automated payment reconciliation'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=1,
            help='Number of days to reconcile (default: 1)'
        )
        
        parser.add_argument(
            '--payment-method',
            type=str,
            choices=['mpesa', 'stripe', 'bank_transfer', 'all'],
            default='all',
            help='Payment method to reconcile'
        )
        
        parser.add_argument(
            '--fix-discrepancies',
            action='store_true',
            help='Attempt to automatically fix discrepancies'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without making changes'
        )
    
    def handle(self, *args, **options):
        days = options['days']
        payment_method = options['payment_method']
        fix_discrepancies = options['fix_discrepancies']
        dry_run = options['dry_run']
        
        self.stdout.write(
            self.style.SUCCESS(f'Starting reconciliation for last {days} days...')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        # Set date range
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        try:
            # Run reconciliation
            results = self._run_reconciliation(
                start_date, end_date, payment_method, fix_discrepancies, dry_run
            )
            
            # Display results
            self._display_results(results)
            
            self.stdout.write(
                self.style.SUCCESS('Reconciliation completed successfully!')
            )
            
        except Exception as e:
            raise CommandError(f'Reconciliation failed: {str(e)}')
    
    def _run_reconciliation(self, start_date, end_date, payment_method, fix_discrepancies, dry_run):
        """Run the actual reconciliation process"""
        results = {
            'period': {
                'start': start_date.date(),
                'end': end_date.date()
            },
            'payment_methods': {},
            'discrepancies': [],
            'fixes_applied': [],
            'summary': {
                'total_transactions': 0,
                'successful_transactions': 0,
                'failed_transactions': 0,
                'pending_transactions': 0,
                'discrepancies_found': 0,
                'discrepancies_fixed': 0
            }
        }
        
        try:
            from payments.models import Transaction
            
            # Get transactions for the period
            transactions = Transaction.objects.filter(
                created_at__range=(start_date, end_date)
            )
            
            if payment_method != 'all':
                transactions = transactions.filter(payment_method=payment_method)
            
            # Group by payment method
            payment_methods = transactions.values_list('payment_method', flat=True).distinct()
            
            for method in payment_methods:
                method_transactions = transactions.filter(payment_method=method)
                method_results = self._reconcile_payment_method(
                    method, method_transactions, fix_discrepancies, dry_run
                )
                results['payment_methods'][method] = method_results
                
                # Update summary
                results['summary']['total_transactions'] += method_results['total']
                results['summary']['successful_transactions'] += method_results['successful']
                results['summary']['failed_transactions'] += method_results['failed']
                results['summary']['pending_transactions'] += method_results['pending']
                results['summary']['discrepancies_found'] += len(method_results['discrepancies'])
                results['summary']['discrepancies_fixed'] += len(method_results['fixes_applied'])
                
                results['discrepancies'].extend(method_results['discrepancies'])
                results['fixes_applied'].extend(method_results['fixes_applied'])
        
        except ImportError:
            # Fallback to activity-based reconciliation
            self.stdout.write(
                self.style.WARNING('Payments app not available, using activity-based reconciliation')
            )
            results = self._reconcile_from_activities(start_date, end_date)
        
        return results
    
    def _reconcile_payment_method(self, method, transactions, fix_discrepancies, dry_run):
        """Reconcile transactions for a specific payment method"""
        method_results = {
            'method': method,
            'total': transactions.count(),
            'successful': transactions.filter(status='completed').count(),
            'failed': transactions.filter(status='failed').count(),
            'pending': transactions.filter(status='pending').count(),
            'discrepancies': [],
            'fixes_applied': []
        }
        
        # Find discrepancies
        discrepancies = self._find_discrepancies(transactions)
        method_results['discrepancies'] = discrepancies
        
        # Attempt to fix discrepancies if requested
        if fix_discrepancies and not dry_run:
            fixes = self._fix_discrepancies(discrepancies)
            method_results['fixes_applied'] = fixes
        
        return method_results
    
    def _find_discrepancies(self, transactions):
        """Find discrepancies in transactions"""
        discrepancies = []
        
        # Find transactions that have been pending for too long
        long_pending = transactions.filter(
            status='pending',
            created_at__lt=timezone.now() - timedelta(hours=24)
        )
        
        for transaction in long_pending:
            discrepancies.append({
                'transaction_id': transaction.id,
                'type': 'long_pending',
                'description': f'Transaction pending for more than 24 hours',
                'amount': float(transaction.amount),
                'created_at': transaction.created_at.isoformat()
            })
        
        # Find failed transactions that might need retry
        recent_failed = transactions.filter(
            status='failed',
            updated_at__lt=timezone.now() - timedelta(hours=1)
        )
        
        for transaction in recent_failed:
            discrepancies.append({
                'transaction_id': transaction.id,
                'type': 'failed_transaction',
                'description': f'Failed transaction that might need investigation',
                'amount': float(transaction.amount),
                'created_at': transaction.created_at.isoformat()
            })
        
        # Find duplicate transactions (same user, amount, and time)
        duplicates = transactions.values(
            'user', 'amount', 'payment_method'
        ).annotate(
            count=Count('id'),
            first_created=Min('created_at')
        ).filter(count__gt=1)
        
        for duplicate in duplicates:
            duplicate_transactions = transactions.filter(
                user=duplicate['user'],
                amount=duplicate['amount'],
                payment_method=duplicate['payment_method']
            )
            
            if duplicate_transactions.count() > 1:
                discrepancies.append({
                    'transaction_ids': list(duplicate_transactions.values_list('id', flat=True)),
                    'type': 'duplicate_transactions',
                    'description': f'Potential duplicate transactions detected',
                    'amount': float(duplicate['amount']),
                    'count': duplicate['count']
                })
        
        return discrepancies
    
    def _fix_discrepancies(self, discrepancies):
        """Attempt to automatically fix discrepancies"""
        fixes_applied = []
        
        for discrepancy in discrepancies:
            if discrepancy['type'] == 'long_pending':
                # For long pending transactions, we might want to check with payment provider
                fix = self._check_payment_provider_status(discrepancy['transaction_id'])
                if fix:
                    fixes_applied.append(fix)
            
            elif discrepancy['type'] == 'failed_transaction':
                # For failed transactions, we might want to retry or mark as resolved
                fix = self._handle_failed_transaction(discrepancy['transaction_id'])
                if fix:
                    fixes_applied.append(fix)
        
        return fixes_applied
    
    def _check_payment_provider_status(self, transaction_id):
        """Check status with payment provider (placeholder)"""
        # This would integrate with actual payment providers
        # For now, just return a placeholder
        return {
            'transaction_id': transaction_id,
            'action': 'status_checked',
            'result': 'Provider status checked - no action needed'
        }
    
    def _handle_failed_transaction(self, transaction_id):
        """Handle failed transaction (placeholder)"""
        # This would implement actual failed transaction handling
        return {
            'transaction_id': transaction_id,
            'action': 'marked_for_review',
            'result': 'Transaction marked for manual review'
        }
    
    def _reconcile_from_activities(self, start_date, end_date):
        """Fallback reconciliation using UserActivity data"""
        activities = UserActivity.objects.filter(
            timestamp__range=(start_date, end_date),
            action_type__in=['deposit', 'withdrawal']
        )
        
        return {
            'period': {
                'start': start_date.date(),
                'end': end_date.date()
            },
            'payment_methods': {
                'activity_based': {
                    'method': 'activity_based',
                    'total': activities.count(),
                    'successful': activities.count(),  # Assume all logged activities are successful
                    'failed': 0,
                    'pending': 0,
                    'discrepancies': [],
                    'fixes_applied': []
                }
            },
            'discrepancies': [],
            'fixes_applied': [],
            'summary': {
                'total_transactions': activities.count(),
                'successful_transactions': activities.count(),
                'failed_transactions': 0,
                'pending_transactions': 0,
                'discrepancies_found': 0,
                'discrepancies_fixed': 0
            }
        }
    
    def _display_results(self, results):
        """Display reconciliation results"""
        self.stdout.write('\n' + '='*50)
        self.stdout.write('RECONCILIATION RESULTS')
        self.stdout.write('='*50)
        
        # Summary
        summary = results['summary']
        self.stdout.write(f"\nPeriod: {results['period']['start']} to {results['period']['end']}")
        self.stdout.write(f"Total Transactions: {summary['total_transactions']}")
        self.stdout.write(f"Successful: {summary['successful_transactions']}")
        self.stdout.write(f"Failed: {summary['failed_transactions']}")
        self.stdout.write(f"Pending: {summary['pending_transactions']}")
        self.stdout.write(f"Discrepancies Found: {summary['discrepancies_found']}")
        self.stdout.write(f"Discrepancies Fixed: {summary['discrepancies_fixed']}")
        
        # Payment method breakdown
        self.stdout.write('\nPayment Method Breakdown:')
        for method, data in results['payment_methods'].items():
            self.stdout.write(f"\n{method.upper()}:")
            self.stdout.write(f"  Total: {data['total']}")
            self.stdout.write(f"  Successful: {data['successful']}")
            self.stdout.write(f"  Failed: {data['failed']}")
            self.stdout.write(f"  Pending: {data['pending']}")
            
            if data['discrepancies']:
                self.stdout.write(f"  Discrepancies: {len(data['discrepancies'])}")
        
        # Discrepancies
        if results['discrepancies']:
            self.stdout.write('\nDiscrepancies Found:')
            for i, discrepancy in enumerate(results['discrepancies'][:10], 1):  # Show first 10
                self.stdout.write(f"{i}. {discrepancy['description']}")
                if 'transaction_id' in discrepancy:
                    self.stdout.write(f"   Transaction ID: {discrepancy['transaction_id']}")
                self.stdout.write(f"   Amount: KSh {discrepancy['amount']}")
        
        # Fixes applied
        if results['fixes_applied']:
            self.stdout.write('\nFixes Applied:')
            for fix in results['fixes_applied']:
                self.stdout.write(f"- {fix['action']}: {fix['result']}")
        
        self.stdout.write('\n' + '='*50)
