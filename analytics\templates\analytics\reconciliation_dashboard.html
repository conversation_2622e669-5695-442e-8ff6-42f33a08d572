{% extends 'analytics/base.html' %}

{% block title %}Reconciliation Dashboard{% endblock %}
{% block page_title %}Payment Reconciliation Dashboard{% endblock %}

{% block content %}
<!-- Summary Cards -->
<div class="row mb-4">
    {% for method, data in reconciliation_data.by_payment_method.items %}
    <div class="col-xl-4 col-md-6 mb-4">
        <div class="card metric-card card-metric h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">{{ method|title }}</div>
                        <div class="h5 mb-0 font-weight-bold">{{ data.success_rate|floatformat:1 }}%</div>
                        <small class="text-light">
                            {{ data.completed }}/{{ data.total_transactions }} successful
                            <br>KSh {{ data.total_amount|floatformat:0 }} processed
                        </small>
                    </div>
                    <div class="col-auto">
                        {% if method == 'mpesa' %}
                            <i class="fas fa-mobile-alt fa-2x"></i>
                        {% elif method == 'stripe' %}
                            <i class="fas fa-credit-card fa-2x"></i>
                        {% else %}
                            <i class="fas fa-university fa-2x"></i>
                        {% endif %}
                    </div>
                </div>
                <div class="mt-2">
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ data.success_rate }}%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Reconciliation Status -->
<div class="row mb-4">
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Payment Method Performance</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Payment Method</th>
                                <th>Total</th>
                                <th>Completed</th>
                                <th>Pending</th>
                                <th>Failed</th>
                                <th>Success Rate</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for method, data in reconciliation_data.by_payment_method.items %}
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ method|title }}</span>
                                </td>
                                <td>{{ data.total_transactions }}</td>
                                <td>
                                    <span class="text-success">
                                        <i class="fas fa-check-circle"></i> {{ data.completed }}
                                    </span>
                                </td>
                                <td>
                                    {% if data.pending > 0 %}
                                        <span class="text-warning">
                                            <i class="fas fa-clock"></i> {{ data.pending }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">0</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if data.failed > 0 %}
                                        <span class="text-danger">
                                            <i class="fas fa-times-circle"></i> {{ data.failed }}
                                        </span>
                                    {% else %}
                                        <span class="text-muted">0</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">{{ data.success_rate|floatformat:1 }}%</span>
                                        <div class="progress flex-grow-1" style="height: 6px;">
                                            <div class="progress-bar {% if data.success_rate >= 95 %}bg-success{% elif data.success_rate >= 85 %}bg-warning{% else %}bg-danger{% endif %}" 
                                                 role="progressbar" style="width: {{ data.success_rate }}%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td>KSh {{ data.total_amount|floatformat:0 }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="text-center">No payment data available</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Reconciliation Actions -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Reconciliation Actions</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <button class="btn btn-primary btn-block w-100 mb-2" onclick="runReconciliation()">
                        <i class="fas fa-sync-alt"></i> Run Full Reconciliation
                    </button>
                </div>
                
                <div class="mb-3">
                    <button class="btn btn-warning btn-block w-100 mb-2" onclick="checkPendingTransactions()">
                        <i class="fas fa-clock"></i> Check Pending Transactions
                    </button>
                </div>
                
                <div class="mb-3">
                    <button class="btn btn-info btn-block w-100 mb-2" onclick="exportReconciliationReport()">
                        <i class="fas fa-download"></i> Export Report
                    </button>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <h6>Quick Stats</h6>
                    <ul class="list-unstyled">
                        <li><strong>Total Discrepancies:</strong> {{ reconciliation_data.total_discrepancies }}</li>
                        <li><strong>Last Reconciliation:</strong> <span class="text-muted">{{ date_range.end }}</span></li>
                        <li><strong>Period:</strong> {{ days }} days</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Discrepancies Table -->
{% if reconciliation_data.discrepancies %}
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-danger">
                    <i class="fas fa-exclamation-triangle"></i> Transactions Requiring Attention
                </h6>
                <span class="badge bg-danger">{{ reconciliation_data.total_discrepancies }}</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Transaction ID</th>
                                <th>User</th>
                                <th>Payment Method</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in reconciliation_data.discrepancies %}
                            <tr>
                                <td>
                                    <code>{{ transaction.id }}</code>
                                </td>
                                <td>{{ transaction.user__phone_number|default:"N/A" }}</td>
                                <td>
                                    <span class="badge bg-secondary">{{ transaction.payment_method|title }}</span>
                                </td>
                                <td>KSh {{ transaction.amount|floatformat:0 }}</td>
                                <td>
                                    {% if transaction.status == 'pending' %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% elif transaction.status == 'failed' %}
                                        <span class="badge bg-danger">Failed</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ transaction.status|title }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ transaction.created_at|date:"M d, Y H:i" }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="investigateTransaction('{{ transaction.id }}')">
                                            <i class="fas fa-search"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-success" 
                                                onclick="retryTransaction('{{ transaction.id }}')">
                                            <i class="fas fa-redo"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="markAsResolved('{{ transaction.id }}')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-success" role="alert">
            <i class="fas fa-check-circle"></i>
            <strong>All Clear!</strong> No discrepancies found in the selected period.
        </div>
    </div>
</div>
{% endif %}

<!-- Reconciliation History -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Reconciliation History</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Discrepancies Found</th>
                                <th>Actions Taken</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{{ date_range.end|date:"M d, Y H:i" }}</td>
                                <td>Automated</td>
                                <td><span class="badge bg-success">Completed</span></td>
                                <td>{{ reconciliation_data.total_discrepancies }}</td>
                                <td>
                                    {% if reconciliation_data.total_discrepancies > 0 %}
                                        <span class="text-warning">Pending Review</span>
                                    {% else %}
                                        <span class="text-success">None Required</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function runReconciliation() {
    if (confirm('This will run a full reconciliation process. Continue?')) {
        // Show loading state
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Running...';
        btn.disabled = true;
        
        // Simulate reconciliation process
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
            alert('Reconciliation completed successfully!');
            location.reload();
        }, 3000);
    }
}

function checkPendingTransactions() {
    alert('Checking pending transactions... This feature would query payment providers for status updates.');
}

function exportReconciliationReport() {
    window.location.href = '{% url "analytics:export_data" %}?type=reconciliation&days={{ days }}';
}

function investigateTransaction(transactionId) {
    alert(`Investigating transaction ${transactionId}... This would open detailed transaction information.`);
}

function retryTransaction(transactionId) {
    if (confirm(`Retry transaction ${transactionId}?`)) {
        alert('Transaction retry initiated. This would attempt to reprocess the payment.');
    }
}

function markAsResolved(transactionId) {
    if (confirm(`Mark transaction ${transactionId} as resolved?`)) {
        alert('Transaction marked as resolved. This would update the transaction status.');
    }
}

// Auto-refresh every 5 minutes
setInterval(() => {
    location.reload();
}, 300000);
</script>
{% endblock %}
