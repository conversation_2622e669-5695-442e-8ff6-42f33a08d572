"""
Input validation and sanitization utilities
"""

import re
import html
import bleach
from typing import Any, Dict, List, Optional, Union
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from decimal import Decimal, InvalidOperation


class InputSanitizer:
    """
    Utility class for sanitizing user input
    """
    
    # Allowed HTML tags for rich text fields
    ALLOWED_TAGS = [
        'p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'a'
    ]
    
    # Allowed HTML attributes
    ALLOWED_ATTRIBUTES = {
        'a': ['href', 'title'],
    }
    
    @staticmethod
    def sanitize_html(text: str) -> str:
        """
        Sanitize HTML content to prevent XSS attacks
        """
        if not text:
            return ""
        
        # Use bleach to clean HTML
        cleaned = bleach.clean(
            text,
            tags=InputSanitizer.ALLOWED_TAGS,
            attributes=InputSanitizer.ALLOWED_ATTRIBUTES,
            strip=True
        )
        
        return cleaned
    
    @staticmethod
    def sanitize_text(text: str) -> str:
        """
        Sanitize plain text input
        """
        if not text:
            return ""
        
        # HTML escape
        sanitized = html.escape(text)
        
        # Remove null bytes and control characters
        sanitized = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', sanitized)
        
        return sanitized.strip()
    
    @staticmethod
    def sanitize_phone_number(phone: str) -> str:
        """
        Sanitize phone number input
        """
        if not phone:
            return ""
        
        # Remove all non-digit characters except +
        sanitized = re.sub(r'[^\d+]', '', phone)
        
        # Ensure it starts with + for international format
        if not sanitized.startswith('+'):
            sanitized = '+' + sanitized
        
        return sanitized
    
    @staticmethod
    def sanitize_decimal(value: Union[str, float, Decimal]) -> Optional[Decimal]:
        """
        Sanitize decimal input for financial amounts
        """
        if value is None or value == "":
            return None
        
        try:
            # Convert to string first to handle floats properly
            str_value = str(value).strip()
            
            # Remove any non-numeric characters except decimal point and minus
            sanitized = re.sub(r'[^\d.-]', '', str_value)
            
            # Convert to Decimal
            decimal_value = Decimal(sanitized)
            
            # Round to 2 decimal places for currency
            return decimal_value.quantize(Decimal('0.01'))
            
        except (InvalidOperation, ValueError):
            return None


class InputValidator:
    """
    Enhanced input validation utilities
    """
    
    # Common regex patterns
    PATTERNS = {
        'phone': r'^\+?1?\d{9,15}$',
        'email': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
        'alphanumeric': r'^[a-zA-Z0-9]+$',
        'alpha': r'^[a-zA-Z]+$',
        'numeric': r'^\d+$',
        'decimal': r'^\d+(\.\d{1,2})?$',
        'bet_code': r'^[A-Z0-9]{6,12}$',
        'ticket_number': r'^BZ-\d{8}-\d{4}$',
    }
    
    @staticmethod
    def validate_phone_number(phone: str) -> bool:
        """
        Validate phone number format
        """
        if not phone:
            return False
        
        sanitized = InputSanitizer.sanitize_phone_number(phone)
        return bool(re.match(InputValidator.PATTERNS['phone'], sanitized))
    
    @staticmethod
    def validate_email_address(email: str) -> bool:
        """
        Validate email address format
        """
        if not email:
            return False
        
        try:
            validate_email(email)
            return True
        except ValidationError:
            return False
    
    @staticmethod
    def validate_betting_amount(amount: Union[str, float, Decimal]) -> tuple[bool, Optional[str]]:
        """
        Validate betting amount
        """
        try:
            decimal_amount = InputSanitizer.sanitize_decimal(amount)
            
            if decimal_amount is None:
                return False, "Invalid amount format"
            
            if decimal_amount <= 0:
                return False, "Amount must be greater than zero"
            
            # Check minimum and maximum limits
            from django.conf import settings
            betting_settings = getattr(settings, 'BETTING_SETTINGS', {})
            
            min_amount = Decimal(str(betting_settings.get('MIN_BET_AMOUNT', 10.0)))
            max_amount = Decimal(str(betting_settings.get('MAX_BET_AMOUNT', 100000.0)))
            
            if decimal_amount < min_amount:
                return False, f"Minimum bet amount is {min_amount}"
            
            if decimal_amount > max_amount:
                return False, f"Maximum bet amount is {max_amount}"
            
            return True, None
            
        except Exception as e:
            return False, f"Invalid amount: {str(e)}"
    
    @staticmethod
    def validate_password_strength(password: str) -> tuple[bool, List[str]]:
        """
        Validate password strength
        """
        errors = []
        
        if len(password) < 8:
            errors.append("Password must be at least 8 characters long")
        
        if not re.search(r'[A-Z]', password):
            errors.append("Password must contain at least one uppercase letter")
        
        if not re.search(r'[a-z]', password):
            errors.append("Password must contain at least one lowercase letter")
        
        if not re.search(r'\d', password):
            errors.append("Password must contain at least one digit")
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("Password must contain at least one special character")
        
        # Check for common weak passwords
        weak_passwords = [
            'password', '12345678', 'qwerty', 'abc123', 'password123',
            'admin', 'letmein', 'welcome', 'monkey', '123456789'
        ]
        
        if password.lower() in weak_passwords:
            errors.append("Password is too common and easily guessable")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def validate_bet_code(code: str) -> bool:
        """
        Validate bet code format
        """
        if not code:
            return False
        
        sanitized = InputSanitizer.sanitize_text(code).upper()
        return bool(re.match(InputValidator.PATTERNS['bet_code'], sanitized))
    
    @staticmethod
    def validate_json_input(data: Any, required_fields: List[str] = None) -> tuple[bool, Optional[str]]:
        """
        Validate JSON input data
        """
        if not isinstance(data, dict):
            return False, "Invalid JSON format"
        
        if required_fields:
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return False, f"Missing required fields: {', '.join(missing_fields)}"
        
        return True, None
    
    @staticmethod
    def validate_file_upload(file, allowed_types: List[str] = None, max_size: int = None) -> tuple[bool, Optional[str]]:
        """
        Validate file upload
        """
        if not file:
            return False, "No file provided"
        
        # Check file size
        if max_size and file.size > max_size:
            return False, f"File size exceeds maximum allowed size of {max_size} bytes"
        
        # Check file type
        if allowed_types:
            file_extension = file.name.split('.')[-1].lower() if '.' in file.name else ''
            if file_extension not in allowed_types:
                return False, f"File type not allowed. Allowed types: {', '.join(allowed_types)}"
        
        # Check for malicious file names
        dangerous_patterns = [
            r'\.\./', r'\.\.\\', r'<script', r'javascript:', r'vbscript:',
            r'onload=', r'onerror=', r'<iframe', r'<object', r'<embed'
        ]
        
        for pattern in dangerous_patterns:
            if re.search(pattern, file.name, re.IGNORECASE):
                return False, "File name contains potentially dangerous content"
        
        return True, None


def validate_form_data(form_data: Dict[str, Any], validation_rules: Dict[str, Dict]) -> Dict[str, List[str]]:
    """
    Validate form data against specified rules
    
    Args:
        form_data: Dictionary of form field values
        validation_rules: Dictionary of validation rules per field
        
    Returns:
        Dictionary of validation errors per field
    """
    errors = {}
    
    for field_name, rules in validation_rules.items():
        field_errors = []
        value = form_data.get(field_name)
        
        # Required field validation
        if rules.get('required', False) and not value:
            field_errors.append(f"{field_name} is required")
            continue
        
        # Skip further validation if field is empty and not required
        if not value:
            continue
        
        # Type validation
        field_type = rules.get('type')
        if field_type == 'email' and not InputValidator.validate_email_address(value):
            field_errors.append("Invalid email format")
        elif field_type == 'phone' and not InputValidator.validate_phone_number(value):
            field_errors.append("Invalid phone number format")
        elif field_type == 'decimal':
            is_valid, error_msg = InputValidator.validate_betting_amount(value)
            if not is_valid:
                field_errors.append(error_msg)
        
        # Length validation
        if 'min_length' in rules and len(str(value)) < rules['min_length']:
            field_errors.append(f"Minimum length is {rules['min_length']} characters")
        
        if 'max_length' in rules and len(str(value)) > rules['max_length']:
            field_errors.append(f"Maximum length is {rules['max_length']} characters")
        
        # Pattern validation
        if 'pattern' in rules and not re.match(rules['pattern'], str(value)):
            field_errors.append(rules.get('pattern_error', "Invalid format"))
        
        if field_errors:
            errors[field_name] = field_errors
    
    return errors
