"""
Admin configuration for analytics models
"""

from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Count, Sum
import json

from .models import (
    UserActivity, BettingPattern, DailyMetrics, 
    SportPopularity, UserSegment, RevenueReport
)


@admin.register(UserActivity)
class UserActivityAdmin(admin.ModelAdmin):
    list_display = ['user', 'action_type', 'timestamp', 'ip_address', 'colored_action_type']
    list_filter = ['action_type', 'timestamp', 'user__is_staff']
    search_fields = ['user__phone_number', 'user__email', 'ip_address', 'session_id']
    readonly_fields = ['timestamp', 'formatted_metadata']
    date_hierarchy = 'timestamp'
    list_per_page = 50
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'session_id', 'action_type', 'timestamp')
        }),
        ('Request Details', {
            'fields': ('ip_address', 'user_agent', 'page_url', 'referrer')
        }),
        ('Additional Data', {
            'fields': ('formatted_metadata', 'response_time'),
            'classes': ('collapse',)
        }),
    )
    
    def colored_action_type(self, obj):
        colors = {
            'login': 'green',
            'logout': 'orange',
            'register': 'blue',
            'bet_placed': 'purple',
            'bet_won': 'green',
            'bet_lost': 'red',
            'deposit': 'blue',
            'withdrawal': 'orange',
        }
        color = colors.get(obj.action_type, 'black')
        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            obj.get_action_type_display()
        )
    colored_action_type.short_description = 'Action Type'
    
    def formatted_metadata(self, obj):
        if obj.metadata:
            return format_html('<pre>{}</pre>', json.dumps(obj.metadata, indent=2))
        return '-'
    formatted_metadata.short_description = 'Metadata'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(BettingPattern)
class BettingPatternAdmin(admin.ModelAdmin):
    list_display = ['user', 'sport', 'bet_type', 'total_bets', 'total_amount_bet', 'win_rate_display', 'roi_display']
    list_filter = ['sport', 'bet_type', 'last_updated']
    search_fields = ['user__phone_number', 'user__email', 'sport', 'league']
    readonly_fields = ['last_updated', 'created_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'sport', 'league', 'bet_type')
        }),
        ('Statistics', {
            'fields': ('total_bets', 'total_amount_bet', 'total_winnings', 'win_rate')
        }),
        ('Preferences', {
            'fields': ('average_stake', 'preferred_odds_range_min', 'preferred_odds_range_max')
        }),
        ('Time Patterns', {
            'fields': ('most_active_hour', 'most_active_day')
        }),
        ('Tracking', {
            'fields': ('last_updated', 'created_at'),
            'classes': ('collapse',)
        }),
    )
    
    def win_rate_display(self, obj):
        if obj.win_rate >= 60:
            color = 'green'
        elif obj.win_rate >= 40:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color,
            obj.win_rate
        )
    win_rate_display.short_description = 'Win Rate'
    
    def roi_display(self, obj):
        if obj.total_amount_bet > 0:
            roi = ((obj.total_winnings - obj.total_amount_bet) / obj.total_amount_bet) * 100
            color = 'green' if roi > 0 else 'red'
            return format_html(
                '<span style="color: {};">{:.1f}%</span>',
                color,
                roi
            )
        return '-'
    roi_display.short_description = 'ROI'


@admin.register(DailyMetrics)
class DailyMetricsAdmin(admin.ModelAdmin):
    list_display = ['date', 'active_users', 'new_registrations', 'total_bets_placed', 'revenue_display', 'profit_margin_display']
    list_filter = ['date']
    search_fields = ['date']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'date'
    
    fieldsets = (
        ('Date', {
            'fields': ('date',)
        }),
        ('User Metrics', {
            'fields': ('new_registrations', 'active_users', 'returning_users')
        }),
        ('Betting Metrics', {
            'fields': ('total_bets_placed', 'total_bet_amount', 'total_winnings_paid')
        }),
        ('Financial Metrics', {
            'fields': ('total_deposits', 'total_withdrawals', 'gross_gaming_revenue')
        }),
        ('Platform Metrics', {
            'fields': ('page_views', 'unique_visitors', 'bounce_rate', 'average_session_duration')
        }),
        ('Support Metrics', {
            'fields': ('support_tickets_created', 'support_tickets_resolved')
        }),
        ('System Metrics', {
            'fields': ('average_response_time', 'error_rate')
        }),
        ('Tracking', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def revenue_display(self, obj):
        return f'KSh {obj.total_bet_amount:,.0f}'
    revenue_display.short_description = 'Revenue'
    
    def profit_margin_display(self, obj):
        margin = obj.profit_margin
        color = 'green' if margin > 10 else 'orange' if margin > 5 else 'red'
        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color,
            margin
        )
    profit_margin_display.short_description = 'Profit Margin'


@admin.register(SportPopularity)
class SportPopularityAdmin(admin.ModelAdmin):
    list_display = ['sport', 'league', 'date', 'total_bets', 'total_amount_bet', 'unique_bettors']
    list_filter = ['sport', 'date']
    search_fields = ['sport', 'league']
    date_hierarchy = 'date'
    
    def get_queryset(self, request):
        return super().get_queryset(request).order_by('-date', '-total_bets')


@admin.register(UserSegment)
class UserSegmentAdmin(admin.ModelAdmin):
    list_display = ['user', 'segment_type', 'confidence_score', 'is_active', 'assigned_at']
    list_filter = ['segment_type', 'is_active', 'assigned_at']
    search_fields = ['user__phone_number', 'user__email']
    readonly_fields = ['assigned_at', 'formatted_criteria']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'segment_type', 'is_active')
        }),
        ('Segment Details', {
            'fields': ('confidence_score', 'formatted_criteria', 'expires_at')
        }),
        ('Tracking', {
            'fields': ('assigned_at',),
            'classes': ('collapse',)
        }),
    )
    
    def formatted_criteria(self, obj):
        if obj.criteria:
            return format_html('<pre>{}</pre>', json.dumps(obj.criteria, indent=2))
        return '-'
    formatted_criteria.short_description = 'Criteria'


@admin.register(RevenueReport)
class RevenueReportAdmin(admin.ModelAdmin):
    list_display = ['report_type', 'period_start', 'period_end', 'gross_revenue_display', 'profit_margin_display']
    list_filter = ['report_type', 'period_start']
    readonly_fields = ['created_at', 'updated_at', 'formatted_metadata']
    date_hierarchy = 'period_start'
    
    fieldsets = (
        ('Report Details', {
            'fields': ('report_type', 'period_start', 'period_end')
        }),
        ('Revenue Metrics', {
            'fields': ('gross_gaming_revenue', 'net_gaming_revenue', 'total_deposits', 'total_withdrawals')
        }),
        ('Cost Metrics', {
            'fields': ('payment_processing_fees', 'operational_costs', 'marketing_costs')
        }),
        ('Calculated Metrics', {
            'fields': ('profit_before_tax', 'profit_margin')
        }),
        ('Additional Data', {
            'fields': ('formatted_metadata',),
            'classes': ('collapse',)
        }),
        ('Tracking', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def gross_revenue_display(self, obj):
        return f'KSh {obj.gross_gaming_revenue:,.0f}'
    gross_revenue_display.short_description = 'Gross Revenue'
    
    def profit_margin_display(self, obj):
        color = 'green' if obj.profit_margin > 10 else 'orange' if obj.profit_margin > 5 else 'red'
        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color,
            obj.profit_margin
        )
    profit_margin_display.short_description = 'Profit Margin'
    
    def formatted_metadata(self, obj):
        if obj.metadata:
            return format_html('<pre>{}</pre>', json.dumps(obj.metadata, indent=2))
        return '-'
    formatted_metadata.short_description = 'Metadata'


# Custom admin site configuration
admin.site.site_header = "Betika Clone Analytics Admin"
admin.site.site_title = "Analytics Admin"
admin.site.index_title = "Analytics Administration"
