#!/usr/bin/env python
"""
Script to generate secure keys for the application
"""

import secrets
import string
from cryptography.fernet import <PERSON><PERSON><PERSON>


def generate_django_secret_key():
    """Generate a secure Django SECRET_KEY"""
    chars = string.ascii_letters + string.digits + '!@#$%^&*(-_=+)'
    return ''.join(secrets.choice(chars) for _ in range(50))


def generate_encryption_key():
    """Generate a secure encryption key for field-level encryption"""
    return Fernet.generate_key().decode('utf-8')


def main():
    print("🔐 Generating secure keys for Betika Clone...")
    print("=" * 50)
    
    # Generate Django SECRET_KEY
    secret_key = generate_django_secret_key()
    print(f"Django SECRET_KEY:")
    print(f"SECRET_KEY={secret_key}")
    print()
    
    # Generate field encryption key
    encryption_key = generate_encryption_key()
    print(f"Field Encryption Key:")
    print(f"FIELD_ENCRYPTION_KEY={encryption_key}")
    print()
    
    print("🔒 Security Notes:")
    print("1. Keep these keys secure and never commit them to version control")
    print("2. Use different keys for development, staging, and production")
    print("3. Store keys in environment variables or secure key management systems")
    print("4. Rotate keys regularly for enhanced security")
    print()
    
    print("📝 Next Steps:")
    print("1. Copy these keys to your .env file")
    print("2. Set DEBUG=False in production")
    print("3. Configure HTTPS/SSL certificates")
    print("4. Run security audit: python manage.py security_check")


if __name__ == "__main__":
    main()
