"""
User models for the betting platform
"""

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator
from django.utils import timezone
from decimal import Decimal
from .managers import CustomUserManager
from core.encryption import SecureDataMixin, field_encryption


class CustomUser(AbstractUser, SecureDataMixin):
    """
    Custom user model using phone number as the primary identifier
    """
    # Remove username field and use phone number instead
    username = None
    
    # Phone number as the primary identifier
    phone_validator = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )
    phone_number = models.CharField(
        validators=[phone_validator],
        max_length=17,
        unique=True,
        help_text="Phone number in international format"
    )
    
    # Additional user fields
    is_verified = models.BooleanField(
        default=False,
        help_text="Indicates if the user's phone number has been verified"
    )
    date_of_birth = models.DateField(
        null=True,
        blank=True,
        help_text="User's date of birth for age verification"
    )
    balance = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="User's current account balance"
    )
    
    # Account status fields
    is_suspended = models.BooleanField(
        default=False,
        help_text="Indicates if the account is temporarily suspended"
    )
    suspension_reason = models.TextField(
        blank=True,
        help_text="Reason for account suspension"
    )
    
    # Timestamps
    verification_sent_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When the last verification SMS was sent"
    )
    last_login_ip = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text="IP address of the last login"
    )

    # Encrypted fields for sensitive data
    phone_number_encrypted = models.TextField(
        blank=True,
        null=True,
        help_text="Encrypted phone number"
    )
    phone_number_hash = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        help_text="Hash for searching encrypted phone number",
        db_index=True
    )
    email_encrypted = models.TextField(
        blank=True,
        null=True,
        help_text="Encrypted email address"
    )
    email_hash = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        help_text="Hash for searching encrypted email",
        db_index=True
    )
    first_name_encrypted = models.TextField(
        blank=True,
        null=True,
        help_text="Encrypted first name"
    )
    last_name_encrypted = models.TextField(
        blank=True,
        null=True,
        help_text="Encrypted last name"
    )
    
    # Use phone number as the username field
    USERNAME_FIELD = 'phone_number'
    REQUIRED_FIELDS = ['email', 'first_name', 'last_name']
    
    # Use custom manager
    objects = CustomUserManager()
    
    class Meta:
        db_table = 'accounts_customuser'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
    
    def __str__(self):
        return self.phone_number
    
    @property
    def full_name(self):
        """Return the user's full name"""
        return f"{self.first_name} {self.last_name}".strip()
    
    @property
    def is_adult(self):
        """Check if user is 18 years or older"""
        if not self.date_of_birth:
            return False
        today = timezone.now().date()
        age = today.year - self.date_of_birth.year
        if today.month < self.date_of_birth.month or \
           (today.month == self.date_of_birth.month and today.day < self.date_of_birth.day):
            age -= 1
        return age >= 18
    
    def can_bet(self):
        """Check if user can place bets"""
        return (
            self.is_verified and 
            self.is_adult and 
            not self.is_suspended and 
            self.is_active
        )
    
    def add_balance(self, amount):
        """Add amount to user balance"""
        self.balance += Decimal(str(amount))
        self.save(update_fields=['balance'])
    
    def subtract_balance(self, amount):
        """Subtract amount from user balance"""
        if self.balance >= Decimal(str(amount)):
            self.balance -= Decimal(str(amount))
            self.save(update_fields=['balance'])
            return True
        return False

    def save(self, *args, **kwargs):
        """
        Override save to handle balance updates, validation, and encryption
        """
        # Ensure balance is never negative
        if self.balance < 0:
            self.balance = Decimal('0.00')

        # Encrypt sensitive fields if they have changed
        self._encrypt_sensitive_fields()

        super().save(*args, **kwargs)

    def _encrypt_sensitive_fields(self):
        """
        Encrypt sensitive fields before saving
        """
        # Encrypt phone number if it has changed
        if self.phone_number and (not self.phone_number_encrypted or
                                 field_encryption.decrypt(self.phone_number_encrypted) != self.phone_number):
            self.encrypt_field('phone_number', self.phone_number)

        # Encrypt email if it has changed
        if self.email and (not self.email_encrypted or
                          field_encryption.decrypt(self.email_encrypted) != self.email):
            self.encrypt_field('email', self.email)

        # Encrypt first name if it has changed
        if self.first_name and (not self.first_name_encrypted or
                               field_encryption.decrypt(self.first_name_encrypted) != self.first_name):
            self.encrypt_field('first_name', self.first_name)

        # Encrypt last name if it has changed
        if self.last_name and (not self.last_name_encrypted or
                              field_encryption.decrypt(self.last_name_encrypted) != self.last_name):
            self.encrypt_field('last_name', self.last_name)


class UserProfile(models.Model):
    """
    Extended user profile information
    """
    KYC_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('in_review', 'In Review'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    ]
    
    LANGUAGE_CHOICES = [
        ('en', 'English'),
        ('sw', 'Swahili'),
    ]
    
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='profile'
    )
    
    # KYC Information
    kyc_status = models.CharField(
        max_length=20,
        choices=KYC_STATUS_CHOICES,
        default='pending'
    )
    id_number = models.CharField(
        max_length=20,
        blank=True,
        help_text="National ID or passport number"
    )
    id_document = models.ImageField(
        upload_to='kyc_documents/',
        blank=True,
        null=True,
        help_text="Upload ID document image"
    )
    
    # Preferences
    preferred_language = models.CharField(
        max_length=10,
        choices=LANGUAGE_CHOICES,
        default='en'
    )
    notification_preferences = models.JSONField(
        default=dict,
        help_text="User notification preferences"
    )
    
    # Address information
    county = models.CharField(max_length=100, blank=True)
    city = models.CharField(max_length=100, blank=True)
    address = models.TextField(blank=True)
    
    # Betting preferences
    favorite_sports = models.JSONField(
        default=list,
        help_text="List of user's favorite sports"
    )
    betting_limits = models.JSONField(
        default=dict,
        help_text="User-defined betting limits"
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'accounts_userprofile'
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'
    
    def __str__(self):
        return f"{self.user.phone_number} Profile"
    
    @property
    def is_kyc_approved(self):
        """Check if KYC is approved"""
        return self.kyc_status == 'approved'


class VerificationCode(models.Model):
    """
    Model to store SMS verification codes
    """
    CODE_TYPE_CHOICES = [
        ('registration', 'Registration'),
        ('password_reset', 'Password Reset'),
        ('phone_verification', 'Phone Verification'),
    ]
    
    user = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='verification_codes'
    )
    code = models.CharField(max_length=6)
    code_type = models.CharField(
        max_length=20,
        choices=CODE_TYPE_CHOICES
    )
    is_used = models.BooleanField(default=False)
    expires_at = models.DateTimeField()
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'accounts_verificationcode'
        verbose_name = 'Verification Code'
        verbose_name_plural = 'Verification Codes'
    
    def __str__(self):
        return f"{self.user.phone_number} - {self.code_type}"
    
    @property
    def is_expired(self):
        """Check if the verification code has expired"""
        return timezone.now() > self.expires_at
    
    @property
    def is_valid(self):
        """Check if the verification code is valid"""
        return not self.is_used and not self.is_expired