# Generated by Django 4.2.7 on 2025-07-21 12:45

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("sports", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Bet",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "bet_type",
                    models.CharField(
                        choices=[
                            ("single", "Single Bet"),
                            ("accumulator", "Accumulator"),
                            ("system", "System Bet"),
                            ("multi", "Multi Bet"),
                            ("combo", "Combo Bet"),
                        ],
                        default="single",
                        max_length=20,
                    ),
                ),
                (
                    "stake",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("1.00"))
                        ],
                    ),
                ),
                (
                    "total_odds",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("1.00"),
                        max_digits=10,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("1.00"))
                        ],
                    ),
                ),
                (
                    "potential_winnings",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=12,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                    ),
                ),
                (
                    "actual_winnings",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=12,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00"))
                        ],
                    ),
                ),
                ("selections_count", models.PositiveIntegerField(default=1)),
                ("winning_selections", models.PositiveIntegerField(default=0)),
                ("void_selections", models.PositiveIntegerField(default=0)),
                ("system_type", models.CharField(blank=True, max_length=20)),
                ("system_combinations", models.PositiveIntegerField(default=0)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("won", "Won"),
                            ("lost", "Lost"),
                            ("void", "Void"),
                            ("partially_won", "Partially Won"),
                            ("partially_void", "Partially Void"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("placed_at", models.DateTimeField(auto_now_add=True)),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="bets",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-placed_at"],
            },
        ),
        migrations.CreateModel(
            name="BetSelection",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "odds_value",
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=8,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("1.00"))
                        ],
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("won", "Won"),
                            ("lost", "Lost"),
                            ("void", "Void"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("settled_at", models.DateTimeField(blank=True, null=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "bet",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="selections",
                        to="betting.bet",
                    ),
                ),
                (
                    "market",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="sports.market"
                    ),
                ),
                (
                    "odds",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="sports.odds"
                    ),
                ),
            ],
            options={
                "ordering": ["created_at"],
                "indexes": [
                    models.Index(
                        fields=["bet", "status"], name="betting_bet_bet_id_aa1fac_idx"
                    ),
                    models.Index(
                        fields=["market", "status"],
                        name="betting_bet_market__7eaabb_idx",
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="bet",
            index=models.Index(
                fields=["user", "status"], name="betting_bet_user_id_3cd6b5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bet",
            index=models.Index(
                fields=["bet_type", "status"], name="betting_bet_bet_typ_8a484e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="bet",
            index=models.Index(
                fields=["placed_at"], name="betting_bet_placed__e5b045_idx"
            ),
        ),
    ]
