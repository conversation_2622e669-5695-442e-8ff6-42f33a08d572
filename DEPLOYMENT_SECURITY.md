# Security Deployment Checklist

## 🚀 Production Deployment Security Guide

This checklist ensures your Betika Clone application is securely deployed to production.

## ✅ Pre-Deployment Security Checklist

### 1. Environment Configuration

- [ ] **Generate Secure Keys**
  ```bash
  python generate_keys.py
  ```
  
- [ ] **Create Production .env File**
  ```bash
  cp .env.example .env
  # Edit .env with production values
  ```

- [ ] **Set Production Environment Variables**
  ```bash
  DEBUG=False
  SECRET_KEY=your-generated-secret-key
  FIELD_ENCRYPTION_KEY=your-generated-encryption-key
  ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com
  ```

### 2. Database Security

- [ ] **Use Strong Database Password**
  ```bash
  DB_PASSWORD=your-strong-database-password
  ```

- [ ] **Enable Database SSL**
  ```bash
  DATABASE_SSL_REQUIRE=True
  ```

- [ ] **Restrict Database Access**
  - Configure firewall rules
  - Use database user with minimal privileges
  - Enable database audit logging

### 3. SSL/HTTPS Configuration

- [ ] **Obtain SSL Certificate**
  - Use Let's Encrypt for free certificates
  - Or purchase from trusted CA

- [ ] **Configure Web Server (Nginx/Apache)**
  ```nginx
  server {
      listen 443 ssl http2;
      server_name yourdomain.com;
      
      ssl_certificate /path/to/certificate.crt;
      ssl_certificate_key /path/to/private.key;
      
      # Security headers
      add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
      add_header X-Frame-Options DENY always;
      add_header X-Content-Type-Options nosniff always;
      
      location / {
          proxy_pass http://127.0.0.1:8000;
          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
      }
  }
  ```

- [ ] **Enable HTTPS Redirects**
  ```python
  # In settings.py
  SECURE_SSL_REDIRECT = True
  SECURE_HSTS_SECONDS = 31536000
  SECURE_HSTS_INCLUDE_SUBDOMAINS = True
  SECURE_HSTS_PRELOAD = True
  ```

### 4. Session Security

- [ ] **Configure Secure Cookies**
  ```python
  SESSION_COOKIE_SECURE = True
  CSRF_COOKIE_SECURE = True
  SESSION_COOKIE_HTTPONLY = True
  CSRF_COOKIE_HTTPONLY = True
  ```

### 5. File Permissions

- [ ] **Set Correct File Permissions**
  ```bash
  # Application files
  chmod 644 *.py
  chmod 755 manage.py
  
  # Settings file
  chmod 600 betika_clone/settings.py
  
  # Media directory
  chmod 755 media/
  
  # Static files
  chmod 755 static/
  ```

### 6. Firewall Configuration

- [ ] **Configure Server Firewall**
  ```bash
  # Allow SSH (change port if needed)
  ufw allow 22/tcp
  
  # Allow HTTP/HTTPS
  ufw allow 80/tcp
  ufw allow 443/tcp
  
  # Allow database (restrict to app server only)
  ufw allow from app_server_ip to any port 5432
  
  # Enable firewall
  ufw enable
  ```

## 🔒 Security Validation

### 1. Run Security Tests

```bash
# Run all security tests
python manage.py test core.tests.test_security

# Run security audit
python manage.py security_check --verbose
```

### 2. Security Scan

```bash
# Check for common vulnerabilities
python manage.py check --deploy

# Run Django security check
python -m pip install django-security
python manage.py security_check
```

### 3. SSL/TLS Testing

- [ ] **Test SSL Configuration**
  - Use SSL Labs Test: https://www.ssllabs.com/ssltest/
  - Verify A+ rating
  - Check certificate chain
  - Verify HSTS headers

### 4. Penetration Testing

- [ ] **Basic Security Tests**
  ```bash
  # Test for common vulnerabilities
  curl -H "X-Forwarded-For: <script>alert('xss')</script>" https://yourdomain.com
  curl -d "username=admin'--" https://yourdomain.com/accounts/login/
  ```

## 🛡️ Monitoring and Maintenance

### 1. Security Monitoring

- [ ] **Set Up Log Monitoring**
  ```python
  # Configure logging in settings.py
  LOGGING = {
      'version': 1,
      'disable_existing_loggers': False,
      'handlers': {
          'security_file': {
              'level': 'WARNING',
              'class': 'logging.FileHandler',
              'filename': '/var/log/betika/security.log',
          },
      },
      'loggers': {
          'django.security': {
              'handlers': ['security_file'],
              'level': 'WARNING',
              'propagate': True,
          },
      },
  }
  ```

- [ ] **Set Up Alerts**
  - Failed login attempts
  - Rate limit violations
  - Security events
  - System errors

### 2. Regular Maintenance

- [ ] **Weekly Tasks**
  - Review security logs
  - Check for failed login attempts
  - Monitor system resources

- [ ] **Monthly Tasks**
  - Update dependencies
  - Review user accounts
  - Rotate encryption keys
  - Security audit

- [ ] **Quarterly Tasks**
  - Penetration testing
  - Security policy review
  - Backup testing
  - Disaster recovery testing

## 🚨 Incident Response

### 1. Security Incident Procedure

1. **Immediate Response**
   - Isolate affected systems
   - Preserve evidence
   - Notify security team

2. **Assessment**
   - Determine scope of breach
   - Identify compromised data
   - Assess business impact

3. **Containment**
   - Stop the attack
   - Prevent further damage
   - Secure systems

4. **Recovery**
   - Restore from clean backups
   - Apply security patches
   - Monitor for reoccurrence

5. **Post-Incident**
   - Document lessons learned
   - Update security measures
   - Notify stakeholders if required

### 2. Emergency Contacts

- **Security Team**: <EMAIL>
- **System Admin**: <EMAIL>
- **Emergency Phone**: +XXX-XXX-XXXX

## 📋 Production Deployment Commands

```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Generate secure keys
python generate_keys.py

# 3. Configure environment
cp .env.example .env
# Edit .env with production values

# 4. Run migrations
python manage.py migrate

# 5. Collect static files
python manage.py collectstatic --noinput

# 6. Create superuser
python manage.py createsuperuser

# 7. Run security check
python manage.py security_check

# 8. Test security
python manage.py test core.tests.test_security

# 9. Start application
gunicorn betika_clone.wsgi:application --bind 0.0.0.0:8000
```

## 🔐 Security Best Practices

1. **Never commit secrets to version control**
2. **Use environment variables for configuration**
3. **Regularly update dependencies**
4. **Monitor security logs**
5. **Implement proper backup strategies**
6. **Use strong authentication**
7. **Encrypt sensitive data**
8. **Regular security audits**
9. **Employee security training**
10. **Incident response planning**

## 📞 Support

For security-related questions or issues:
- Email: <EMAIL>
- Documentation: See SECURITY.md
- Emergency: Follow incident response procedure

---

**Remember**: Security is an ongoing process, not a one-time setup. Regular monitoring, updates, and audits are essential for maintaining a secure application.
