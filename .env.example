# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration
USE_POSTGRES=False
DB_NAME=betika_clone
DB_USER=postgres
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# M-Pesa Configuration
MPESA_CONSUMER_KEY=your-mpesa-consumer-key
MPESA_CONSUMER_SECRET=your-mpesa-consumer-secret
MPESA_BUSINESS_SHORT_CODE=your-business-short-code
MPESA_PASSKEY=your-mpesa-passkey
MPESA_ENVIRONMENT=sandbox

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key

# Security Configuration
FIELD_ENCRYPTION_KEY=your-field-encryption-key-here-change-this-in-production

# Rate Limiting Configuration
RATE_LIMIT_API=60
RATE_LIMIT_AUTH=10
RATE_LIMIT_PAYMENT=5
RATE_LIMIT_GLOBAL=100

# Security Settings
MAX_LOGIN_ATTEMPTS=5
LOGIN_LOCKOUT_DURATION=900
SESSION_TIMEOUT=1800

# File Upload Security
MAX_UPLOAD_SIZE=10485760
ALLOWED_UPLOAD_EXTENSIONS=jpg,jpeg,png,pdf,doc,docx

# Session Security (Production only)
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False