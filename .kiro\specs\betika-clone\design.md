# Design Document - Betika Clone

## Overview

The Betika clone will be built as a modern web application using Django as the backend framework with HTML, JavaScript, and CSS for the frontend. The system follows a Model-View-Template (MVT) architecture pattern with additional API endpoints for AJAX interactions. The platform will handle high-frequency real-time data updates for live betting, secure payment processing, and comprehensive user management.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Frontend - HTML/JS/CSS] --> B[Django Views/Templates]
    B --> C[Django Models/ORM]
    C --> D[PostgreSQL Database]
    B --> E[Django REST API]
    E --> F[External APIs]
    F --> G[Sports Data API]
    F --> H[M-Pesa API]
    F --> I[Payment Gateways]
    B --> J[Django Channels]
    J --> K[WebSocket Connections]
    K --> L[Real-time Updates]
```

### System Components

1. **Web Layer**: Django templates with HTML/CSS/JavaScript
2. **Application Layer**: Django views and business logic
3. **Data Layer**: Django ORM with PostgreSQL
4. **Real-time Layer**: Django Channels for WebSocket connections
5. **Integration Layer**: External API integrations
6. **Security Layer**: Django authentication and custom security middleware

## Components and Interfaces

### Django Apps Structure

```
betika_clone/
├── accounts/          # User authentication and profiles
├── betting/           # Core betting functionality
├── sports/            # Sports events and odds management
├── payments/          # Payment processing and wallet
├── live_betting/      # Real-time betting features
├── jackpots/          # Jackpot and multi-bet features
├── support/           # Customer support system
├── admin_panel/       # Administrative interface
├── api/               # REST API endpoints
└── core/              # Shared utilities and base classes
```

### Key Models

#### User Management (accounts app)
```python
class CustomUser(AbstractUser):
    phone_number = models.CharField(max_length=15, unique=True)
    is_verified = models.BooleanField(default=False)
    date_of_birth = models.DateField()
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)

class UserProfile:
    user = models.OneToOneField(CustomUser)
    kyc_status = models.CharField(max_length=20)
    preferred_language = models.CharField(max_length=10)
    notification_preferences = models.JSONField()
```

#### Sports and Events (sports app)
```python
class Sport:
    name = models.CharField(max_length=100)
    slug = models.SlugField()
    is_active = models.BooleanField(default=True)

class Event:
    sport = models.ForeignKey(Sport)
    home_team = models.CharField(max_length=100)
    away_team = models.CharField(max_length=100)
    start_time = models.DateTimeField()
    status = models.CharField(max_length=20)  # upcoming, live, finished
    
class Market:
    event = models.ForeignKey(Event)
    market_type = models.CharField(max_length=50)  # 1X2, Over/Under, etc.
    is_active = models.BooleanField(default=True)

class Odds:
    market = models.ForeignKey(Market)
    selection = models.CharField(max_length=100)
    odds_value = models.DecimalField(max_digits=6, decimal_places=2)
    last_updated = models.DateTimeField(auto_now=True)
```

#### Betting System (betting app)
```python
class Bet:
    user = models.ForeignKey(CustomUser)
    bet_type = models.CharField(max_length=20)  # single, multi, jackpot
    stake = models.DecimalField(max_digits=8, decimal_places=2)
    potential_winnings = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20)  # pending, won, lost, void
    placed_at = models.DateTimeField(auto_now_add=True)

class BetSelection:
    bet = models.ForeignKey(Bet)
    market = models.ForeignKey(Market)
    selection = models.CharField(max_length=100)
    odds_at_placement = models.DecimalField(max_digits=6, decimal_places=2)
    result = models.CharField(max_length=20)  # won, lost, void, pending
```

### Frontend Architecture

#### UI/UX Design System

**Design Theme**: Dark theme with green accent colors matching Betika's visual identity

**Color Palette**:
```css
:root {
    --primary-dark: #1a1a1a;        /* Main background */
    --secondary-dark: #2d2d2d;      /* Card backgrounds */
    --accent-green: #4CAF50;        /* Primary green */
    --accent-light-green: #66BB6A;  /* Hover states */
    --text-primary: #ffffff;        /* Primary text */
    --text-secondary: #b0b0b0;      /* Secondary text */
    --border-color: #404040;        /* Borders and dividers */
    --warning-orange: #FF9800;      /* Warnings and highlights */
}
```

**Layout Structure**:
1. **Header Navigation**: Top bar with logo, main navigation tabs, user account info
2. **Left Sidebar**: Collapsible sports categories navigation
3. **Main Content Area**: Event listings, odds display, promotional banners
4. **Right Sidebar**: Persistent bet slip panel
5. **Footer**: Links and additional information

#### JavaScript Structure
```
static/js/
├── core/
│   ├── api.js              # AJAX API calls
│   ├── websocket.js        # WebSocket connections
│   ├── utils.js            # Utility functions
│   └── betika-theme.js     # Theme and UI utilities
├── betting/
│   ├── betslip.js          # Bet slip management
│   ├── odds-display.js     # Odds rendering and interaction
│   ├── live-betting.js     # Live betting features
│   └── market-expansion.js # "+186 Markets" functionality
├── navigation/
│   ├── sidebar.js          # Sports sidebar navigation
│   ├── tabs.js             # Main navigation tabs
│   └── filters.js          # Event filtering and search
├── payments/
│   ├── deposit.js          # Deposit functionality
│   └── withdrawal.js       # Withdrawal functionality
└── main.js                 # Main application entry point
```

#### CSS Architecture
```
static/css/
├── base.css               # Base styles and CSS variables
├── theme/
│   ├── betika-theme.css   # Betika-specific color scheme
│   ├── dark-mode.css      # Dark theme implementation
│   └── typography.css     # Font styles and text hierarchy
├── layout/
│   ├── header.css         # Top navigation bar
│   ├── sidebar.css        # Left sports navigation
│   ├── main-content.css   # Central content area
│   └── betslip.css        # Right bet slip panel
├── components/
│   ├── buttons.css        # Button styles (green theme)
│   ├── odds-buttons.css   # Betting odds display buttons
│   ├── cards.css          # Event and match cards
│   ├── tabs.css           # Navigation tabs styling
│   ├── banners.css        # Promotional banner styles
│   └── modals.css         # Modal dialogs
├── pages/
│   ├── home.css           # Homepage with highlights
│   ├── sportsbook.css     # Main betting interface
│   ├── live-betting.css   # Live events styling
│   └── account.css        # Account management pages
└── responsive.css         # Mobile responsiveness
```

### User Interface Components

#### 1. Header Navigation
- **Logo**: "Betika!" branding on the left
- **Main Tabs**: Home, Live (49), Jackpots, Shikisha Bet (6), Aviator, Ligi Bora, Casino, Promotions (11), Virtuals, Betika Fast, Crash Games, Live Score, App
- **User Section**: Login/Register buttons or user account dropdown
- **Search**: Global search functionality
- **Theme Toggle**: Light/dark mode switcher

#### 2. Left Sidebar Navigation
- **Sports Categories**:
  - Soccer (primary sport)
  - Boxing
  - Rugby
  - Aussie Rules
  - Baseball
  - Table Tennis
  - Cricket
  - Tennis
  - MMA
  - Basketball
  - Water polo
  - Volleyball
  - eSoccer
  - Handball
  - Darts
- **Collapsible**: Hamburger menu for mobile
- **Active State**: Highlight selected sport

#### 3. Main Content Area
- **Promotional Banner**: Large banner with current promotions (e.g., "PUNGUZA MOTO ya Bet kuchomeka na up to 5X STAKE BACK BONUS!")
- **Event Tabs**: Highlights, Upcoming, Countries, Quick-e
- **Filters**: Date, highlights toggle, match count selector
- **Event Listings**: 
  - Match cards with team names
  - Start times
  - Three-column odds layout (1, X, 2)
  - "+186 Markets" expandable options
  - Live indicators for ongoing matches

#### 4. Right Sidebar - Bet Slip
- **Header**: "Load Betslip" with code input
- **Selections**: List of selected bets
- **Stake Input**: Amount input field
- **Bet Type**: Single/Multi bet toggle
- **Potential Winnings**: Calculated automatically
- **Place Bet Button**: Primary action button
- **Clear All**: Option to clear selections

#### 5. Odds Display System
- **Three-Column Layout**: 1 (Home), X (Draw), 2 (Away)
- **Odds Values**: Decimal format (e.g., 1.70, 3.55, 5.60)
- **Interactive States**:
  - Default: White background, dark text
  - Hover: Green background
  - Selected: Green background, white text
  - Changed: Highlight with animation
- **Market Expansion**: Click to show additional markets

### Responsive Design Strategy

#### Breakpoints
- **Desktop**: 1200px and above
- **Tablet**: 768px - 1199px
- **Mobile**: Below 768px

#### Mobile Adaptations
- **Sidebar**: Converts to slide-out menu
- **Bet Slip**: Becomes bottom sheet/modal
- **Odds**: Stack vertically on small screens
- **Navigation**: Hamburger menu with full-screen overlay
- **Touch Targets**: Minimum 44px for touch interactions

## Data Models

### Database Schema Design

The system uses PostgreSQL with the following key relationships:

1. **User-centric design**: All betting activities link to CustomUser
2. **Hierarchical sports structure**: Sport → Event → Market → Odds
3. **Flexible betting system**: Support for single, multi, and jackpot bets
4. **Audit trail**: All financial transactions logged with timestamps
5. **Real-time data**: Optimized for frequent odds updates

### Key Relationships

- Users have many Bets (One-to-Many)
- Bets have many BetSelections (One-to-Many)
- Events have many Markets (One-to-Many)
- Markets have many Odds (One-to-Many)
- Users have many Transactions (One-to-Many)

## Error Handling

### Backend Error Handling

1. **Custom Exception Classes**:
   ```python
   class BettingException(Exception):
       pass
   
   class InsufficientBalanceException(BettingException):
       pass
   
   class InvalidOddsException(BettingException):
       pass
   ```

2. **Middleware for Global Error Handling**:
   - Log all errors with context
   - Return appropriate HTTP status codes
   - Sanitize error messages for security

3. **Validation Framework**:
   - Django Forms for user input validation
   - Custom validators for betting rules
   - Real-time validation for odds changes

### Frontend Error Handling

1. **AJAX Error Handling**:
   ```javascript
   function handleApiError(xhr, status, error) {
       if (xhr.status === 400) {
           showValidationErrors(xhr.responseJSON.errors);
       } else if (xhr.status === 401) {
           redirectToLogin();
       } else {
           showGenericError('Something went wrong. Please try again.');
       }
   }
   ```

2. **WebSocket Error Handling**:
   - Automatic reconnection logic
   - Graceful degradation when real-time features fail
   - User notifications for connection issues

## Testing Strategy

### Backend Testing

1. **Unit Tests**:
   - Model validation and business logic
   - View functionality and permissions
   - API endpoint responses
   - Payment processing logic

2. **Integration Tests**:
   - Database operations
   - External API integrations
   - WebSocket connections
   - End-to-end betting workflows

3. **Performance Tests**:
   - Database query optimization
   - Concurrent user handling
   - Real-time update performance

### Frontend Testing

1. **JavaScript Unit Tests**:
   - Utility functions
   - API interaction logic
   - Bet slip calculations
   - Form validation

2. **UI/UX Testing**:
   - Cross-browser compatibility
   - Mobile responsiveness
   - Accessibility compliance
   - User interaction flows

### Security Testing

1. **Authentication Testing**:
   - Login/logout functionality
   - Session management
   - Password security

2. **Authorization Testing**:
   - User permission checks
   - Admin panel access control
   - API endpoint security

3. **Input Validation Testing**:
   - SQL injection prevention
   - XSS protection
   - CSRF token validation

## Performance Considerations

### Database Optimization

1. **Indexing Strategy**:
   - Index on frequently queried fields (user_id, event_id, status)
   - Composite indexes for complex queries
   - Partial indexes for filtered queries

2. **Query Optimization**:
   - Use select_related() and prefetch_related() for related objects
   - Database connection pooling
   - Query result caching for static data

### Caching Strategy

1. **Redis Integration**:
   - Cache frequently accessed odds data
   - Session storage for better performance
   - Real-time data caching with TTL

2. **Django Caching Framework**:
   - Template fragment caching
   - View-level caching for static content
   - Database query result caching

### Real-time Performance

1. **WebSocket Optimization**:
   - Connection pooling and management
   - Selective data broadcasting
   - Client-side data throttling

2. **Frontend Performance**:
   - JavaScript code minification
   - CSS optimization and compression
   - Image optimization and lazy loading

## Security Implementation

### Authentication & Authorization

1. **Django Authentication System**:
   - Custom user model with phone number as username
   - JWT tokens for API authentication
   - Session-based authentication for web interface

2. **Permission System**:
   - Role-based access control
   - Custom permissions for betting operations
   - Admin panel access restrictions

### Data Protection

1. **Encryption**:
   - HTTPS for all communications
   - Database field encryption for sensitive data
   - Secure password hashing with Django's built-in system

2. **Input Validation**:
   - Django Forms for server-side validation
   - JavaScript validation for user experience
   - SQL injection prevention through ORM

### Financial Security

1. **Payment Security**:
   - PCI DSS compliance for card payments
   - Secure API integration with payment providers
   - Transaction logging and audit trails

2. **Fraud Prevention**:
   - Rate limiting for betting operations
   - Suspicious activity detection
   - Account verification requirements

## Deployment Architecture

### Production Environment

1. **Server Setup**:
   - Django application server (Gunicorn)
   - Nginx as reverse proxy and static file server
   - PostgreSQL database server
   - Redis for caching and session storage

2. **Scalability Considerations**:
   - Load balancing for multiple Django instances
   - Database read replicas for performance
   - CDN for static asset delivery

3. **Monitoring and Logging**:
   - Application performance monitoring
   - Error tracking and alerting
   - User activity logging for compliance