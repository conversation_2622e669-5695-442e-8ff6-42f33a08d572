{% extends 'analytics/base.html' %}

{% block title %}Transaction Reports{% endblock %}
{% block page_title %}Transaction Reports{% endblock %}

{% block content %}
<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="days" class="form-label">Time Period</label>
                        <select class="form-select" id="days" name="days">
                            <option value="7" {% if filters.days == 7 %}selected{% endif %}>Last 7 days</option>
                            <option value="30" {% if filters.days == 30 %}selected{% endif %}>Last 30 days</option>
                            <option value="90" {% if filters.days == 90 %}selected{% endif %}>Last 90 days</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="payment_method" class="form-label">Payment Method</label>
                        <select class="form-select" id="payment_method" name="payment_method">
                            <option value="">All Methods</option>
                            {% for method in payment_methods %}
                            <option value="{{ method }}" {% if filters.payment_method == method %}selected{% endif %}>
                                {{ method|title }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="transaction_type" class="form-label">Transaction Type</label>
                        <select class="form-select" id="transaction_type" name="transaction_type">
                            <option value="">All Types</option>
                            <option value="deposit" {% if filters.transaction_type == 'deposit' %}selected{% endif %}>Deposits</option>
                            <option value="withdrawal" {% if filters.transaction_type == 'withdrawal' %}selected{% endif %}>Withdrawals</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary mt-4">Apply Filters</button>
                        <a href="{% url 'analytics:transaction_reports' %}" class="btn btn-secondary mt-4">Reset</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card card-metric h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Deposits</div>
                        <div class="h5 mb-0 font-weight-bold">KSh {{ summary.deposits.total_amount|floatformat:0 }}</div>
                        <small class="text-light">{{ summary.deposits.count }} transactions</small>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-arrow-down fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card card-metric-warning h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Withdrawals</div>
                        <div class="h5 mb-0 font-weight-bold">KSh {{ summary.withdrawals.total_amount|floatformat:0 }}</div>
                        <small class="text-light">{{ summary.withdrawals.count }} transactions</small>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-arrow-up fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card card-metric-success h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Net Cash Flow</div>
                        <div class="h5 mb-0 font-weight-bold {% if summary.net_cash_flow < 0 %}text-danger{% endif %}">
                            KSh {{ summary.net_cash_flow|floatformat:0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-balance-scale fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card card-metric-info h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Transaction Volume</div>
                        <div class="h5 mb-0 font-weight-bold">{{ summary.transaction_volume }}</div>
                        <small class="text-light">Total transactions</small>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exchange-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Daily Transaction Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Daily Transaction Trends</h6>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="dailyTransactionChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Payment Methods Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Payment Methods</h6>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="paymentMethodChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Users Table -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Top Users by Transaction Volume</h6>
                <a href="{% url 'analytics:export_data' %}?type=user_transactions&days={{ filters.days }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-download"></i> Export
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Total Transactions</th>
                                <th>Deposits</th>
                                <th>Withdrawals</th>
                                <th>Total Amount</th>
                                <th>Net Position</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in user_analysis %}
                            <tr>
                                <td>
                                    <a href="{% url 'analytics:user_detail_analytics' user.user_id %}">
                                        {{ user.phone_number }}
                                    </a>
                                </td>
                                <td>{{ user.total_transactions }}</td>
                                <td>{{ user.deposits }} (KSh {{ user.deposit_amount|floatformat:0 }})</td>
                                <td>{{ user.withdrawals }} (KSh {{ user.withdrawal_amount|floatformat:0 }})</td>
                                <td>KSh {{ user.total_amount|floatformat:0 }}</td>
                                <td class="{% if user.net_position < 0 %}text-danger{% else %}text-success{% endif %}">
                                    KSh {{ user.net_position|floatformat:0 }}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center">No transaction data available</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Growth Trends -->
{% if trends.weekly_breakdown %}
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Weekly Growth Trends</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Week</th>
                                        <th>Transactions</th>
                                        <th>Amount</th>
                                        <th>Growth</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for week in trends.weekly_breakdown %}
                                    <tr>
                                        <td>{{ week.week_start }} - {{ week.week_end }}</td>
                                        <td>{{ week.transaction_count }}</td>
                                        <td>KSh {{ week.total_amount|floatformat:0 }}</td>
                                        <td>
                                            {% if forloop.counter0 < trends.growth_rates|length %}
                                                <span class="{% if trends.growth_rates|slice:forloop.counter0|first > 0 %}text-success{% else %}text-danger{% endif %}">
                                                    {{ trends.growth_rates|slice:forloop.counter0|first|floatformat:1 }}%
                                                </span>
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="{% if trends.average_growth_rate > 0 %}text-success{% else %}text-danger{% endif %}">
                                {{ trends.average_growth_rate|floatformat:1 }}%
                            </h4>
                            <p class="text-muted">Average Weekly Growth</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// Daily Transaction Chart
const dailyCtx = document.getElementById('dailyTransactionChart').getContext('2d');
const dailyData = {{ daily_breakdown|safe }};

new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: dailyData.map(d => d.date),
        datasets: [
            {
                label: 'Deposits',
                data: dailyData.map(d => d.deposits.amount),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            },
            {
                label: 'Withdrawals',
                data: dailyData.map(d => d.withdrawals.amount),
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1
            },
            {
                label: 'Net Flow',
                data: dailyData.map(d => d.net_flow),
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                tension: 0.1
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Payment Methods Chart
const paymentCtx = document.getElementById('paymentMethodChart').getContext('2d');
const paymentData = {{ summary.payment_methods|safe }};

if (paymentData && paymentData.length > 0) {
    new Chart(paymentCtx, {
        type: 'doughnut',
        data: {
            labels: paymentData.map(p => p.payment_method),
            datasets: [{
                data: paymentData.map(p => p.total_amount),
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(153, 102, 255, 0.8)',
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}
</script>
{% endblock %}
