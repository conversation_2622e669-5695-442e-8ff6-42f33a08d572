"""
Comprehensive tests for analytics and reporting functionality
"""

from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
import json

from .models import (
    UserActivity, BettingPattern, DailyMetrics, 
    SportPopularity, UserSegment, RevenueReport
)
from .utils import (
    calculate_daily_metrics, get_platform_analytics_summary,
    get_user_analytics_summary, update_user_segments
)
from .financial_reports import TransactionReporter, generate_transaction_report
from .profit_loss import ProfitLossCalculator, generate_profit_loss_report
from .exporters import AnalyticsExporter, JSONExporter, CSVExporter

User = get_user_model()


class AnalyticsModelsTest(TestCase):
    """Test analytics models"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+254700000001',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_user_activity_creation(self):
        """Test UserActivity model creation"""
        activity = UserActivity.objects.create(
            user=self.user,
            action_type='login',
            ip_address='127.0.0.1',
            metadata={'device': 'mobile'}
        )
        
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.action_type, 'login')
        self.assertEqual(activity.ip_address, '127.0.0.1')
        self.assertEqual(activity.metadata['device'], 'mobile')
        self.assertTrue(activity.timestamp)
    
    def test_betting_pattern_creation(self):
        """Test BettingPattern model creation"""
        pattern = BettingPattern.objects.create(
            user=self.user,
            sport='football',
            bet_type='single',
            total_bets=10,
            total_amount_bet=Decimal('1000.00'),
            total_winnings=Decimal('800.00'),
            win_rate=80.0
        )
        
        self.assertEqual(pattern.user, self.user)
        self.assertEqual(pattern.sport, 'football')
        self.assertEqual(pattern.total_bets, 10)
        self.assertEqual(pattern.win_rate, 80.0)
    
    def test_daily_metrics_creation(self):
        """Test DailyMetrics model creation"""
        today = timezone.now().date()
        metrics = DailyMetrics.objects.create(
            date=today,
            new_registrations=5,
            active_users=100,
            total_bets_placed=50,
            total_bet_amount=Decimal('5000.00')
        )
        
        self.assertEqual(metrics.date, today)
        self.assertEqual(metrics.new_registrations, 5)
        self.assertEqual(metrics.active_users, 100)
        self.assertTrue(metrics.profit_margin >= 0)
    
    def test_user_segment_creation(self):
        """Test UserSegment model creation"""
        segment = UserSegment.objects.create(
            user=self.user,
            segment_type='high_value',
            confidence_score=0.95,
            criteria={'total_bets': 100, 'total_amount': 10000}
        )
        
        self.assertEqual(segment.user, self.user)
        self.assertEqual(segment.segment_type, 'high_value')
        self.assertEqual(segment.confidence_score, 0.95)
        self.assertTrue(segment.is_active)


class AnalyticsUtilsTest(TestCase):
    """Test analytics utility functions"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+254700000002',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test data
        self.create_test_activities()
    
    def create_test_activities(self):
        """Create test user activities"""
        today = timezone.now()
        
        # Create various activities
        UserActivity.objects.create(
            user=self.user,
            action_type='login',
            timestamp=today
        )
        
        UserActivity.objects.create(
            user=self.user,
            action_type='bet_placed',
            timestamp=today,
            metadata={'stake': '100.00', 'sport': 'football'}
        )
        
        UserActivity.objects.create(
            user=self.user,
            action_type='bet_won',
            timestamp=today,
            metadata={'winnings': '180.00'}
        )
    
    def test_calculate_daily_metrics(self):
        """Test daily metrics calculation"""
        today = timezone.now().date()
        metrics = calculate_daily_metrics(today)
        
        self.assertIsInstance(metrics, DailyMetrics)
        self.assertEqual(metrics.date, today)
        self.assertGreaterEqual(metrics.active_users, 0)
    
    def test_get_user_analytics_summary(self):
        """Test user analytics summary"""
        summary = get_user_analytics_summary(self.user, days=7)
        
        self.assertIn('total_activities', summary)
        self.assertIn('activity_breakdown', summary)
        self.assertIn('recent_activities', summary)
        self.assertGreater(summary['total_activities'], 0)
    
    def test_get_platform_analytics_summary(self):
        """Test platform analytics summary"""
        summary = get_platform_analytics_summary(days=7)
        
        self.assertIn('totals', summary)
        self.assertIn('daily_metrics', summary)
        self.assertIn('period_days', summary)
        self.assertEqual(summary['period_days'], 7)


class FinancialReportsTest(TestCase):
    """Test financial reporting functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+254700000003',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test transaction activities
        self.create_test_transactions()
    
    def create_test_transactions(self):
        """Create test transaction activities"""
        today = timezone.now()
        
        UserActivity.objects.create(
            user=self.user,
            action_type='deposit',
            timestamp=today,
            metadata={'amount': '1000.00', 'payment_method': 'mpesa'}
        )
        
        UserActivity.objects.create(
            user=self.user,
            action_type='withdrawal',
            timestamp=today,
            metadata={'amount': '500.00', 'payment_method': 'mpesa'}
        )
    
    def test_transaction_reporter(self):
        """Test TransactionReporter functionality"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=7)
        
        reporter = TransactionReporter(start_date, end_date)
        summary = reporter.get_transaction_summary()
        
        self.assertIn('period', summary)
        self.assertIn('deposits', summary)
        self.assertIn('withdrawals', summary)
        self.assertIn('net_cash_flow', summary)
    
    def test_generate_transaction_report(self):
        """Test transaction report generation"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=7)
        
        report = generate_transaction_report(start_date, end_date)
        
        self.assertIn('summary', report)
        self.assertIn('daily_breakdown', report)
        self.assertIn('top_users', report)
        self.assertIn('generated_at', report)


class ProfitLossTest(TestCase):
    """Test profit and loss calculations"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+254700000004',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test betting activities
        self.create_test_betting_data()
    
    def create_test_betting_data(self):
        """Create test betting data"""
        today = timezone.now()
        
        UserActivity.objects.create(
            user=self.user,
            action_type='bet_placed',
            timestamp=today,
            metadata={'stake': '100.00', 'sport': 'football'}
        )
        
        UserActivity.objects.create(
            user=self.user,
            action_type='bet_won',
            timestamp=today,
            metadata={'winnings': '80.00'}
        )
    
    def test_profit_loss_calculator(self):
        """Test ProfitLossCalculator functionality"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=7)
        
        calculator = ProfitLossCalculator(start_date, end_date)
        gaming_revenue = calculator.calculate_gaming_revenue()
        
        self.assertIn('total_stakes', gaming_revenue)
        self.assertIn('total_winnings_paid', gaming_revenue)
        self.assertIn('gross_gaming_revenue', gaming_revenue)
        self.assertGreaterEqual(gaming_revenue['total_stakes'], 0)
    
    def test_operational_costs_calculation(self):
        """Test operational costs calculation"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=7)
        
        calculator = ProfitLossCalculator(start_date, end_date)
        costs = calculator.calculate_operational_costs()
        
        self.assertIn('breakdown', costs)
        self.assertIn('total_operational_costs', costs)
        self.assertIn('cost_percentage', costs)
        self.assertGreaterEqual(costs['total_operational_costs'], 0)
    
    def test_profit_loss_calculation(self):
        """Test comprehensive P&L calculation"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=7)
        
        calculator = ProfitLossCalculator(start_date, end_date)
        pl_report = calculator.calculate_profit_loss()
        
        self.assertIn('revenue', pl_report)
        self.assertIn('costs', pl_report)
        self.assertIn('profit', pl_report)
        self.assertIn('margins', pl_report)
        self.assertIn('kpis', pl_report)


class ExportersTest(TestCase):
    """Test data export functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+254700000005',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test data
        self.activity = UserActivity.objects.create(
            user=self.user,
            action_type='login',
            ip_address='127.0.0.1'
        )
    
    def test_json_exporter(self):
        """Test JSON export functionality"""
        queryset = UserActivity.objects.filter(user=self.user)
        exporter = JSONExporter(queryset, 'test_export')
        
        response = exporter.export()
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIn('attachment', response['Content-Disposition'])
    
    def test_csv_exporter(self):
        """Test CSV export functionality"""
        queryset = UserActivity.objects.filter(user=self.user)
        fields = ['user__phone_number', 'action_type', 'timestamp']
        exporter = CSVExporter(queryset, 'test_export', fields)
        
        response = exporter.export()
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('attachment', response['Content-Disposition'])
    
    def test_analytics_exporter(self):
        """Test AnalyticsExporter functionality"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=7)
        
        response = AnalyticsExporter.export_user_activities(
            start_date, end_date, 'json'
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')


class AnalyticsViewsTest(TestCase):
    """Test analytics views"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            phone_number='+254700000006',
            password='testpass123'
        )
        self.staff_user = User.objects.create_user(
            phone_number='+254700000007',
            password='testpass123',
            is_staff=True
        )
    
    def test_analytics_dashboard_access(self):
        """Test analytics dashboard access"""
        # Test unauthorized access
        response = self.client.get(reverse('analytics:dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Test authorized access
        self.client.login(phone_number='+254700000007', password='testpass123')
        response = self.client.get(reverse('analytics:dashboard'))
        self.assertEqual(response.status_code, 200)
    
    def test_user_analytics_view(self):
        """Test user analytics view"""
        self.client.login(phone_number='+254700000007', password='testpass123')
        
        # Test user list view
        response = self.client.get(reverse('analytics:user_analytics'))
        self.assertEqual(response.status_code, 200)
        
        # Test specific user view
        response = self.client.get(
            reverse('analytics:user_detail_analytics', args=[self.user.id])
        )
        self.assertEqual(response.status_code, 200)
    
    def test_financial_reports_view(self):
        """Test financial reports view"""
        self.client.login(phone_number='+254700000007', password='testpass123')
        response = self.client.get(reverse('analytics:financial_reports'))
        self.assertEqual(response.status_code, 200)
    
    def test_api_analytics_data(self):
        """Test analytics API endpoint"""
        self.client.login(phone_number='+254700000007', password='testpass123')
        
        response = self.client.get(
            reverse('analytics:api_data'),
            {'type': 'daily_metrics', 'days': 7}
        )
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.content)
        self.assertIn('labels', data)
        self.assertIn('datasets', data)
    
    def test_export_data_view(self):
        """Test data export view"""
        self.client.login(phone_number='+254700000007', password='testpass123')
        
        response = self.client.get(
            reverse('analytics:export_data'),
            {'type': 'user_activities', 'format': 'json', 'days': 7}
        )
        self.assertEqual(response.status_code, 200)
    
    def test_personal_analytics_view(self):
        """Test personal analytics view for regular users"""
        self.client.login(phone_number='+254700000006', password='testpass123')
        response = self.client.get(reverse('analytics:personal_analytics'))
        self.assertEqual(response.status_code, 200)


class ManagementCommandsTest(TestCase):
    """Test management commands"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+254700000008',
            password='testpass123'
        )
    
    def test_update_daily_metrics_command(self):
        """Test update_daily_metrics management command"""
        from django.core.management import call_command
        from io import StringIO
        
        out = StringIO()
        call_command('update_daily_metrics', '--days=1', stdout=out)
        
        output = out.getvalue()
        self.assertIn('Daily metrics updated successfully', output)
    
    def test_generate_reports_command(self):
        """Test generate_reports management command"""
        from django.core.management import call_command
        from io import StringIO
        
        out = StringIO()
        call_command('generate_reports', '--type=daily', stdout=out)
        
        output = out.getvalue()
        self.assertIn('Reports generated successfully', output)


class IntegrationTest(TestCase):
    """Integration tests for the complete analytics system"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+254700000009',
            password='testpass123'
        )
        self.staff_user = User.objects.create_user(
            phone_number='+254700000010',
            password='testpass123',
            is_staff=True
        )
        
        # Create comprehensive test data
        self.create_comprehensive_test_data()
    
    def create_comprehensive_test_data(self):
        """Create comprehensive test data for integration testing"""
        today = timezone.now()
        
        # User activities
        activities = [
            ('login', {}),
            ('bet_placed', {'stake': '100.00', 'sport': 'football'}),
            ('bet_won', {'winnings': '180.00'}),
            ('deposit', {'amount': '1000.00', 'payment_method': 'mpesa'}),
            ('withdrawal', {'amount': '500.00', 'payment_method': 'mpesa'}),
        ]
        
        for action_type, metadata in activities:
            UserActivity.objects.create(
                user=self.user,
                action_type=action_type,
                timestamp=today,
                metadata=metadata
            )
        
        # Betting patterns
        BettingPattern.objects.create(
            user=self.user,
            sport='football',
            bet_type='single',
            total_bets=10,
            total_amount_bet=Decimal('1000.00'),
            total_winnings=Decimal('800.00'),
            win_rate=80.0
        )
        
        # User segment
        UserSegment.objects.create(
            user=self.user,
            segment_type='frequent_bettor',
            confidence_score=0.9
        )
    
    def test_complete_analytics_workflow(self):
        """Test complete analytics workflow"""
        # 1. Calculate daily metrics
        today = timezone.now().date()
        metrics = calculate_daily_metrics(today)
        self.assertIsInstance(metrics, DailyMetrics)
        
        # 2. Generate platform summary
        summary = get_platform_analytics_summary(days=7)
        self.assertIn('totals', summary)
        
        # 3. Generate user summary
        user_summary = get_user_analytics_summary(self.user, days=7)
        self.assertGreater(user_summary['total_activities'], 0)
        
        # 4. Generate transaction report
        end_date = timezone.now()
        start_date = end_date - timedelta(days=7)
        transaction_report = generate_transaction_report(start_date, end_date)
        self.assertIn('summary', transaction_report)
        
        # 5. Generate P&L report
        pl_report = generate_profit_loss_report(start_date, end_date)
        self.assertIn('profit_loss', pl_report)
        
        # 6. Test export functionality
        response = AnalyticsExporter.export_user_activities(
            start_date, end_date, 'json'
        )
        self.assertEqual(response.status_code, 200)
    
    def test_dashboard_integration(self):
        """Test dashboard integration with real data"""
        self.client = Client()
        self.client.login(phone_number='+254700000010', password='testpass123')
        
        # Test main dashboard
        response = self.client.get(reverse('analytics:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Analytics Dashboard')
        
        # Test financial reports
        response = self.client.get(reverse('analytics:financial_reports'))
        self.assertEqual(response.status_code, 200)
        
        # Test transaction reports
        response = self.client.get(reverse('analytics:transaction_reports'))
        self.assertEqual(response.status_code, 200)
        
        # Test P&L dashboard
        response = self.client.get(reverse('analytics:profit_loss_dashboard'))
        self.assertEqual(response.status_code, 200)
