"""
Profit and Loss calculation utilities for the betting platform
"""

from django.db.models import Sum, Count, Avg, Q, F
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
import json

from .models import DailyMetrics, RevenueReport, UserActivity, BettingPattern

User = get_user_model()


class ProfitLossCalculator:
    """
    Comprehensive profit and loss calculation system
    """
    
    def __init__(self, start_date=None, end_date=None):
        self.start_date = start_date or (timezone.now() - timedelta(days=30))
        self.end_date = end_date or timezone.now()
    
    def calculate_gaming_revenue(self) -> Dict:
        """Calculate gaming revenue (betting revenue)"""
        try:
            # Try to get betting data from betting app
            from betting.models import Bet
            
            bets = Bet.objects.filter(
                created_at__range=(self.start_date, self.end_date),
                status__in=['won', 'lost', 'settled']
            )
            
            # Calculate gross gaming revenue (total stakes minus winnings paid)
            total_stakes = bets.aggregate(total=Sum('stake'))['total'] or Decimal('0.00')
            total_winnings = bets.filter(status='won').aggregate(
                total=Sum('winnings')
            )['total'] or Decimal('0.00')
            
            gross_gaming_revenue = total_stakes - total_winnings
            
            # Calculate by sport
            sport_revenue = {}
            for bet in bets.select_related('selections__market__event__sport'):
                if hasattr(bet, 'selections') and bet.selections.exists():
                    first_selection = bet.selections.first()
                    if hasattr(first_selection, 'market') and hasattr(first_selection.market, 'event'):
                        sport = first_selection.market.event.sport.name
                        if sport not in sport_revenue:
                            sport_revenue[sport] = {
                                'stakes': Decimal('0.00'),
                                'winnings': Decimal('0.00'),
                                'revenue': Decimal('0.00'),
                                'bet_count': 0
                            }
                        
                        sport_revenue[sport]['stakes'] += bet.stake
                        if bet.status == 'won' and hasattr(bet, 'winnings'):
                            sport_revenue[sport]['winnings'] += bet.winnings or Decimal('0.00')
                        sport_revenue[sport]['bet_count'] += 1
            
            # Calculate revenue for each sport
            for sport in sport_revenue:
                sport_revenue[sport]['revenue'] = (
                    sport_revenue[sport]['stakes'] - sport_revenue[sport]['winnings']
                )
            
            return {
                'total_stakes': float(total_stakes),
                'total_winnings_paid': float(total_winnings),
                'gross_gaming_revenue': float(gross_gaming_revenue),
                'total_bets': bets.count(),
                'won_bets': bets.filter(status='won').count(),
                'lost_bets': bets.filter(status='lost').count(),
                'win_rate': (bets.filter(status='won').count() / bets.count() * 100) if bets.count() > 0 else 0,
                'sport_breakdown': {
                    sport: {
                        'stakes': float(data['stakes']),
                        'winnings': float(data['winnings']),
                        'revenue': float(data['revenue']),
                        'bet_count': data['bet_count'],
                        'margin': float((data['revenue'] / data['stakes']) * 100) if data['stakes'] > 0 else 0
                    }
                    for sport, data in sport_revenue.items()
                }
            }
            
        except ImportError:
            # Fallback using analytics data
            return self._calculate_gaming_revenue_from_analytics()
    
    def _calculate_gaming_revenue_from_analytics(self) -> Dict:
        """Fallback method using analytics data"""
        # Get betting activities
        bet_activities = UserActivity.objects.filter(
            timestamp__range=(self.start_date, self.end_date),
            action_type='bet_placed'
        )
        
        won_activities = UserActivity.objects.filter(
            timestamp__range=(self.start_date, self.end_date),
            action_type='bet_won'
        )
        
        # Extract amounts from metadata
        total_stakes = Decimal('0.00')
        total_winnings = Decimal('0.00')
        
        for activity in bet_activities:
            if 'stake' in activity.metadata:
                try:
                    total_stakes += Decimal(str(activity.metadata['stake']))
                except (ValueError, TypeError):
                    pass
        
        for activity in won_activities:
            if 'winnings' in activity.metadata:
                try:
                    total_winnings += Decimal(str(activity.metadata['winnings']))
                except (ValueError, TypeError):
                    pass
        
        gross_gaming_revenue = total_stakes - total_winnings
        
        return {
            'total_stakes': float(total_stakes),
            'total_winnings_paid': float(total_winnings),
            'gross_gaming_revenue': float(gross_gaming_revenue),
            'total_bets': bet_activities.count(),
            'won_bets': won_activities.count(),
            'lost_bets': bet_activities.count() - won_activities.count(),
            'win_rate': (won_activities.count() / bet_activities.count() * 100) if bet_activities.count() > 0 else 0,
            'sport_breakdown': {}
        }
    
    def calculate_operational_costs(self) -> Dict:
        """Calculate operational costs"""
        # These would typically come from a costs management system
        # For now, we'll use estimated percentages of revenue
        
        gaming_revenue = self.calculate_gaming_revenue()
        gross_revenue = Decimal(str(gaming_revenue['gross_gaming_revenue']))
        
        # Estimated operational costs as percentages
        costs = {
            'payment_processing': gross_revenue * Decimal('0.025'),  # 2.5% of revenue
            'platform_maintenance': gross_revenue * Decimal('0.05'),  # 5% of revenue
            'customer_support': gross_revenue * Decimal('0.03'),  # 3% of revenue
            'marketing': gross_revenue * Decimal('0.15'),  # 15% of revenue
            'compliance_licensing': gross_revenue * Decimal('0.02'),  # 2% of revenue
            'staff_salaries': gross_revenue * Decimal('0.20'),  # 20% of revenue
            'infrastructure': gross_revenue * Decimal('0.08'),  # 8% of revenue
            'other_expenses': gross_revenue * Decimal('0.05'),  # 5% of revenue
        }
        
        total_costs = sum(costs.values())
        
        return {
            'breakdown': {k: float(v) for k, v in costs.items()},
            'total_operational_costs': float(total_costs),
            'cost_percentage': float((total_costs / gross_revenue) * 100) if gross_revenue > 0 else 0
        }
    
    def calculate_profit_loss(self) -> Dict:
        """Calculate comprehensive profit and loss"""
        gaming_revenue = self.calculate_gaming_revenue()
        operational_costs = self.calculate_operational_costs()
        
        # Get transaction data for additional revenue streams
        try:
            from payments.models import Transaction
            
            # Calculate transaction fees (if any)
            transactions = Transaction.objects.filter(
                created_at__range=(self.start_date, self.end_date),
                status='completed'
            )
            
            # Assume small transaction fees
            transaction_fee_revenue = transactions.count() * Decimal('5.00')  # KSh 5 per transaction
            
        except ImportError:
            transaction_fee_revenue = Decimal('0.00')
        
        # Revenue calculations
        gross_gaming_revenue = Decimal(str(gaming_revenue['gross_gaming_revenue']))
        other_revenue = transaction_fee_revenue
        total_revenue = gross_gaming_revenue + other_revenue
        
        # Cost calculations
        total_costs = Decimal(str(operational_costs['total_operational_costs']))
        
        # Profit calculations
        ebitda = total_revenue - total_costs  # Earnings before interest, taxes, depreciation, amortization
        
        # Assume some additional expenses
        interest_expense = total_revenue * Decimal('0.01')  # 1% interest
        depreciation = total_revenue * Decimal('0.02')  # 2% depreciation
        tax_rate = Decimal('0.30')  # 30% tax rate
        
        ebit = ebitda - depreciation  # Earnings before interest and taxes
        ebt = ebit - interest_expense  # Earnings before taxes
        net_profit = ebt * (Decimal('1.00') - tax_rate)  # Net profit after taxes
        
        # Calculate margins
        gross_margin = (gross_gaming_revenue / total_revenue * 100) if total_revenue > 0 else 0
        operating_margin = (ebitda / total_revenue * 100) if total_revenue > 0 else 0
        net_margin = (net_profit / total_revenue * 100) if total_revenue > 0 else 0
        
        return {
            'period': {
                'start': self.start_date.date(),
                'end': self.end_date.date(),
                'days': (self.end_date.date() - self.start_date.date()).days
            },
            'revenue': {
                'gross_gaming_revenue': float(gross_gaming_revenue),
                'transaction_fees': float(other_revenue),
                'total_revenue': float(total_revenue)
            },
            'costs': {
                'operational_costs': operational_costs['breakdown'],
                'total_operational_costs': float(total_costs),
                'interest_expense': float(interest_expense),
                'depreciation': float(depreciation)
            },
            'profit': {
                'ebitda': float(ebitda),
                'ebit': float(ebit),
                'ebt': float(ebt),
                'net_profit': float(net_profit),
                'tax_expense': float(ebt * tax_rate)
            },
            'margins': {
                'gross_margin': float(gross_margin),
                'operating_margin': float(operating_margin),
                'net_margin': float(net_margin)
            },
            'kpis': {
                'revenue_per_user': float(total_revenue / gaming_revenue['total_bets']) if gaming_revenue['total_bets'] > 0 else 0,
                'cost_per_acquisition': float(operational_costs['breakdown']['marketing'] / gaming_revenue['total_bets']) if gaming_revenue['total_bets'] > 0 else 0,
                'return_on_revenue': float(net_profit / total_revenue * 100) if total_revenue > 0 else 0
            },
            'gaming_metrics': gaming_revenue
        }
    
    def get_monthly_comparison(self, months=6) -> List[Dict]:
        """Get monthly P&L comparison"""
        monthly_data = []
        
        for i in range(months):
            month_end = timezone.now().replace(day=1) - timedelta(days=i*30)
            month_start = month_end - timedelta(days=30)
            
            # Create calculator for this month
            month_calculator = ProfitLossCalculator(month_start, month_end)
            month_pl = month_calculator.calculate_profit_loss()
            
            monthly_data.append({
                'month': month_start.strftime('%Y-%m'),
                'revenue': month_pl['revenue']['total_revenue'],
                'costs': month_pl['costs']['total_operational_costs'],
                'profit': month_pl['profit']['net_profit'],
                'margin': month_pl['margins']['net_margin']
            })
        
        return list(reversed(monthly_data))  # Chronological order


def generate_profit_loss_report(start_date=None, end_date=None, format='dict') -> Dict:
    """Generate comprehensive P&L report"""
    calculator = ProfitLossCalculator(start_date, end_date)
    
    report = {
        'profit_loss': calculator.calculate_profit_loss(),
        'monthly_comparison': calculator.get_monthly_comparison(),
        'generated_at': timezone.now().isoformat()
    }
    
    if format == 'json':
        return json.dumps(report, default=str, indent=2)
    
    return report
