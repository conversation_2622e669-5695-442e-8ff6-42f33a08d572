"""
Comprehensive security tests for authentication and data protection
"""

import time
import json
from decimal import Decimal
from django.test import <PERSON><PERSON><PERSON>, Client, override_settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.files.uploadedfile import SimpleUploadedFile
from django.urls import reverse
from unittest.mock import patch, MagicMock

from core.middleware import RateLimitMiddleware, LoginAttemptMiddleware, SessionSecurityMiddleware
from core.validators import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, InputValidator
from core.encryption import field_encryption, FieldEncryption
from core.file_security import SecureFileUploadHandler, validate_uploaded_file

User = get_user_model()


class SecurityMiddlewareTests(TestCase):
    """
    Test security middleware functionality
    """
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='TestPass123!',
            first_name='Test',
            last_name='User'
        )
    
    def test_rate_limiting_middleware(self):
        """Test rate limiting functionality"""
        middleware = RateLimitMiddleware(lambda r: None)
        
        # Clear cache
        cache.clear()
        
        # Test normal requests within limit
        for i in range(5):
            request = self.client.get('/api/v1/accounts/login/').wsgi_request
            request.user = self.user
            response = middleware.process_request(request)
            self.assertIsNone(response)  # Should not be rate limited
        
        # Test rate limit exceeded
        request = self.client.get('/api/v1/accounts/login/').wsgi_request
        request.user = self.user
        response = middleware.process_request(request)
        self.assertEqual(response.status_code, 429)
    
    def test_login_attempt_middleware(self):
        """Test login attempt monitoring"""
        middleware = LoginAttemptMiddleware(lambda r: None)
        
        # Clear cache
        cache.clear()
        
        # Simulate failed login attempts
        for i in range(5):
            response = self.client.post('/accounts/login/', {
                'username': '+************',
                'password': 'wrongpassword'
            })
            
            # Process response through middleware
            request = response.wsgi_request
            middleware.process_response(request, response)
        
        # Next attempt should be blocked
        response = self.client.post('/accounts/login/', {
            'username': '+************',
            'password': 'wrongpassword'
        })
        
        request = response.wsgi_request
        blocked_response = middleware.process_request(request)
        self.assertEqual(blocked_response.status_code, 423)
    
    def test_session_security_middleware(self):
        """Test session security and timeout"""
        middleware = SessionSecurityMiddleware(lambda r: None)
        
        # Login user
        self.client.login(phone_number='+************', password='TestPass123!')
        
        # Get session
        session = self.client.session
        session['last_activity'] = time.time() - 2000  # 33+ minutes ago
        session.save()
        
        # Create request
        request = self.client.get('/').wsgi_request
        request.user = self.user
        request.session = session
        
        # Should detect expired session
        response = middleware.process_request(request)
        self.assertEqual(response.status_code, 401)


class InputValidationTests(TestCase):
    """
    Test input validation and sanitization
    """
    
    def test_input_sanitizer(self):
        """Test input sanitization"""
        # Test HTML sanitization
        malicious_html = '<script>alert("xss")</script><p>Safe content</p>'
        sanitized = InputSanitizer.sanitize_html(malicious_html)
        self.assertNotIn('<script>', sanitized)
        self.assertIn('<p>Safe content</p>', sanitized)
        
        # Test text sanitization
        malicious_text = '<script>alert("xss")</script>Normal text'
        sanitized = InputSanitizer.sanitize_text(malicious_text)
        self.assertNotIn('<script>', sanitized)
        self.assertIn('Normal text', sanitized)
        
        # Test phone number sanitization
        phone = '0700-000-000'
        sanitized = InputSanitizer.sanitize_phone_number(phone)
        self.assertEqual(sanitized, '+************')
        
        # Test decimal sanitization
        amount = '1,234.56'
        sanitized = InputSanitizer.sanitize_decimal(amount)
        self.assertEqual(sanitized, Decimal('1234.56'))
    
    def test_input_validator(self):
        """Test input validation"""
        # Test phone number validation
        self.assertTrue(InputValidator.validate_phone_number('+************'))
        self.assertFalse(InputValidator.validate_phone_number('invalid'))
        
        # Test email validation
        self.assertTrue(InputValidator.validate_email_address('<EMAIL>'))
        self.assertFalse(InputValidator.validate_email_address('invalid-email'))
        
        # Test betting amount validation
        is_valid, error = InputValidator.validate_betting_amount('100.00')
        self.assertTrue(is_valid)
        self.assertIsNone(error)
        
        is_valid, error = InputValidator.validate_betting_amount('5.00')  # Below minimum
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
        
        # Test password strength
        is_strong, errors = InputValidator.validate_password_strength('WeakPass')
        self.assertFalse(is_strong)
        self.assertTrue(len(errors) > 0)
        
        is_strong, errors = InputValidator.validate_password_strength('StrongPass123!')
        self.assertTrue(is_strong)
        self.assertEqual(len(errors), 0)


class EncryptionTests(TestCase):
    """
    Test field-level encryption
    """
    
    def test_field_encryption(self):
        """Test encryption and decryption"""
        original_value = "sensitive data"
        
        # Test encryption
        encrypted = field_encryption.encrypt(original_value)
        self.assertIsNotNone(encrypted)
        self.assertNotEqual(encrypted, original_value)
        
        # Test decryption
        decrypted = field_encryption.decrypt(encrypted)
        self.assertEqual(decrypted, original_value)
        
        # Test hash generation
        hash_value = field_encryption.hash_value(original_value)
        self.assertIsNotNone(hash_value)
        self.assertEqual(len(hash_value), 64)  # SHA-256 hex length
    
    def test_user_encryption(self):
        """Test user model encryption"""
        user = User.objects.create_user(
            phone_number='+254700000001',
            email='<EMAIL>',
            password='TestPass123!',
            first_name='Encrypted',
            last_name='User'
        )
        
        # Check that encrypted fields are populated
        self.assertIsNotNone(user.phone_number_encrypted)
        self.assertIsNotNone(user.phone_number_hash)
        self.assertIsNotNone(user.email_encrypted)
        self.assertIsNotNone(user.email_hash)
        
        # Test decryption methods
        self.assertEqual(user.get_encrypted_phone_number(), '+254700000001')
        self.assertEqual(user.get_encrypted_email(), '<EMAIL>')
        
        # Test search by encrypted fields
        found_users = User.find_by_phone_number('+254700000001')
        self.assertTrue(found_users.exists())
        
        found_users = User.find_by_email('<EMAIL>')
        self.assertTrue(found_users.exists())


class FileSecurityTests(TestCase):
    """
    Test secure file upload handling
    """
    
    def test_secure_file_upload_handler(self):
        """Test file upload validation"""
        handler = SecureFileUploadHandler('images')
        
        # Test valid image file
        valid_image = SimpleUploadedFile(
            "test.jpg",
            b'\xff\xd8\xff\xe0\x00\x10JFIF',  # JPEG magic bytes
            content_type="image/jpeg"
        )
        
        is_valid, error = handler.validate_file(valid_image)
        self.assertTrue(is_valid)
        self.assertIsNone(error)
        
        # Test invalid file type
        invalid_file = SimpleUploadedFile(
            "test.exe",
            b'MZ\x90\x00',  # PE executable magic bytes
            content_type="application/octet-stream"
        )
        
        is_valid, error = handler.validate_file(invalid_file)
        self.assertFalse(is_valid)
        self.assertIsNotNone(error)
        
        # Test oversized file
        large_file = SimpleUploadedFile(
            "large.jpg",
            b'x' * (10 * 1024 * 1024),  # 10MB
            content_type="image/jpeg"
        )
        
        is_valid, error = handler.validate_file(large_file)
        self.assertFalse(is_valid)
        self.assertIn('size exceeds', error)
    
    def test_malicious_file_detection(self):
        """Test detection of malicious files"""
        handler = SecureFileUploadHandler('documents')
        
        # Test file with script content
        malicious_file = SimpleUploadedFile(
            "malicious.txt",
            b'<script>alert("xss")</script>',
            content_type="text/plain"
        )
        
        is_valid, error = handler.validate_file(malicious_file)
        self.assertFalse(is_valid)
        self.assertIn('malicious', error.lower())
    
    def test_filename_validation(self):
        """Test filename security validation"""
        handler = SecureFileUploadHandler('documents')
        
        # Test path traversal attempt
        self.assertFalse(handler._validate_filename('../../../etc/passwd'))
        self.assertFalse(handler._validate_filename('..\\..\\windows\\system32'))
        
        # Test valid filename
        self.assertTrue(handler._validate_filename('document.pdf'))
        self.assertTrue(handler._validate_filename('my_file_123.txt'))
        
        # Test dangerous characters
        self.assertFalse(handler._validate_filename('file<script>.txt'))
        self.assertFalse(handler._validate_filename('file|pipe.txt'))


class AuthenticationSecurityTests(TestCase):
    """
    Test authentication security features
    """
    
    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='TestPass123!',
            first_name='Auth',
            last_name='User'
        )
    
    def test_password_hashing(self):
        """Test password is properly hashed"""
        self.assertNotEqual(self.user.password, 'TestPass123!')
        self.assertTrue(self.user.check_password('TestPass123!'))
        self.assertFalse(self.user.check_password('wrongpassword'))
    
    def test_session_security(self):
        """Test session security settings"""
        # Login user
        response = self.client.post('/accounts/login/', {
            'username': '+************',
            'password': 'TestPass123!'
        })
        
        # Check session cookie settings
        session_cookie = response.cookies.get('betika_sessionid')
        if session_cookie:
            self.assertTrue(session_cookie['httponly'])
            self.assertEqual(session_cookie['samesite'], 'Lax')
    
    @patch('accounts.utils.log_security_event')
    def test_security_event_logging(self, mock_log):
        """Test security event logging"""
        # Attempt login with wrong password
        self.client.post('/accounts/login/', {
            'username': '+************',
            'password': 'wrongpassword'
        })
        
        # Should log failed login attempt
        # Note: This test depends on the login view implementation
        # mock_log.assert_called()


class CSRFProtectionTests(TestCase):
    """
    Test CSRF protection
    """
    
    def test_csrf_protection_enabled(self):
        """Test CSRF protection is enabled"""
        # POST request without CSRF token should fail
        response = self.client.post('/accounts/login/', {
            'username': '+************',
            'password': 'TestPass123!'
        })
        
        # Should get CSRF error (403) or form error
        self.assertIn(response.status_code, [403, 200])  # 200 with form errors


class XSSProtectionTests(TestCase):
    """
    Test XSS protection
    """
    
    def test_xss_in_form_input(self):
        """Test XSS protection in form inputs"""
        malicious_input = '<script>alert("xss")</script>'
        
        # Test in registration form
        response = self.client.post('/accounts/register/', {
            'phone_number': '+************',
            'email': '<EMAIL>',
            'first_name': malicious_input,
            'last_name': 'User',
            'password1': 'TestPass123!',
            'password2': 'TestPass123!',
            'date_of_birth': '1990-01-01'
        })
        
        # Check that script tags are not in response
        self.assertNotIn('<script>', response.content.decode())


@override_settings(DEBUG=False)
class SecurityHeadersTests(TestCase):
    """
    Test security headers
    """
    
    def test_security_headers_present(self):
        """Test that security headers are present"""
        response = self.client.get('/')
        
        # Check for security headers
        expected_headers = [
            'X-Content-Type-Options',
            'X-Frame-Options',
            'X-XSS-Protection',
            'Content-Security-Policy',
            'Referrer-Policy'
        ]
        
        for header in expected_headers:
            self.assertIn(header, response)
    
    def test_csp_header(self):
        """Test Content Security Policy header"""
        response = self.client.get('/')
        csp = response.get('Content-Security-Policy', '')
        
        # Check for important CSP directives
        self.assertIn("default-src 'self'", csp)
        self.assertIn("frame-ancestors 'none'", csp)
        self.assertIn("base-uri 'self'", csp)


class SQLInjectionTests(TestCase):
    """
    Test SQL injection protection
    """

    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='TestPass123!',
            first_name='SQL',
            last_name='User'
        )

    def test_sql_injection_in_login(self):
        """Test SQL injection protection in login"""
        # Attempt SQL injection in username field
        malicious_username = "'; DROP TABLE accounts_customuser; --"

        response = self.client.post('/accounts/login/', {
            'username': malicious_username,
            'password': 'anything'
        })

        # Should not cause database error and user table should still exist
        self.assertTrue(User.objects.exists())
        self.assertEqual(response.status_code, 200)  # Form with errors

    def test_sql_injection_in_search(self):
        """Test SQL injection protection in search functionality"""
        # This would test search endpoints if they exist
        # For now, just test that ORM usage prevents SQL injection

        # Attempt to use raw SQL injection patterns
        malicious_query = "1' OR '1'='1"

        # Using Django ORM should be safe
        try:
            users = User.objects.filter(first_name=malicious_query)
            # Should return empty queryset, not all users
            self.assertEqual(users.count(), 0)
        except Exception:
            # Should not cause database errors
            self.fail("SQL injection attempt caused database error")


class SessionFixationTests(TestCase):
    """
    Test session fixation protection
    """

    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='TestPass123!',
            first_name='Session',
            last_name='User'
        )

    def test_session_regeneration_on_login(self):
        """Test that session ID changes on login"""
        # Get initial session
        response = self.client.get('/accounts/login/')
        initial_session_key = self.client.session.session_key

        # Login
        response = self.client.post('/accounts/login/', {
            'username': '+************',
            'password': 'TestPass123!'
        })

        # Session key should change after login
        new_session_key = self.client.session.session_key
        self.assertNotEqual(initial_session_key, new_session_key)


class ClickjackingTests(TestCase):
    """
    Test clickjacking protection
    """

    def test_x_frame_options_header(self):
        """Test X-Frame-Options header is set"""
        response = self.client.get('/')
        self.assertEqual(response.get('X-Frame-Options'), 'DENY')


class SecurityConfigurationTests(TestCase):
    """
    Test security configuration
    """

    def test_debug_disabled_in_production(self):
        """Test DEBUG is disabled in production"""
        from django.conf import settings

        # In production, DEBUG should be False
        # This test assumes production settings
        if not settings.DEBUG:
            self.assertFalse(settings.DEBUG)

    def test_secret_key_strength(self):
        """Test SECRET_KEY is strong"""
        from django.conf import settings

        # Check SECRET_KEY length and complexity
        self.assertGreaterEqual(len(settings.SECRET_KEY), 50)

        # Should contain mixed characters
        has_upper = any(c.isupper() for c in settings.SECRET_KEY)
        has_lower = any(c.islower() for c in settings.SECRET_KEY)
        has_digit = any(c.isdigit() for c in settings.SECRET_KEY)
        has_special = any(not c.isalnum() for c in settings.SECRET_KEY)

        self.assertTrue(has_upper or has_lower)  # At least letters
        self.assertTrue(has_digit or has_special)  # At least numbers or special chars

    def test_allowed_hosts_configured(self):
        """Test ALLOWED_HOSTS is properly configured"""
        from django.conf import settings

        # ALLOWED_HOSTS should not be empty in production
        self.assertIsInstance(settings.ALLOWED_HOSTS, list)

        # Should not contain wildcard in production
        if not settings.DEBUG:
            self.assertNotIn('*', settings.ALLOWED_HOSTS)


class DataLeakageTests(TestCase):
    """
    Test for potential data leakage
    """

    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+254700000006',
            email='<EMAIL>',
            password='TestPass123!',
            first_name='Leak',
            last_name='User'
        )

    def test_password_not_in_serialized_data(self):
        """Test password is not exposed in API responses"""
        # Login first
        self.client.login(phone_number='+254700000006', password='TestPass123!')

        # Make API request (if user profile endpoint exists)
        # This would test actual API endpoints
        # For now, just test that password field is not serialized

        from django.core import serializers
        serialized = serializers.serialize('json', [self.user])
        self.assertNotIn('TestPass123!', serialized)
        self.assertNotIn(self.user.password, serialized)

    def test_sensitive_data_not_in_logs(self):
        """Test sensitive data is not logged"""
        # This would require checking actual log output
        # For now, just ensure logging is configured properly
        import logging

        logger = logging.getLogger('django.security')
        self.assertIsNotNone(logger)

    def test_error_messages_dont_leak_info(self):
        """Test error messages don't leak sensitive information"""
        # Test login with non-existent user
        response = self.client.post('/accounts/login/', {
            'username': '+************',
            'password': 'anything'
        })

        # Error message should not reveal if user exists
        content = response.content.decode()
        self.assertNotIn('user does not exist', content.lower())
        self.assertNotIn('invalid user', content.lower())


class BruteForceProtectionTests(TestCase):
    """
    Test brute force attack protection
    """

    def setUp(self):
        self.user = User.objects.create_user(
            phone_number='+************',
            email='<EMAIL>',
            password='TestPass123!',
            first_name='Brute',
            last_name='User'
        )
        cache.clear()

    def test_account_lockout_after_failed_attempts(self):
        """Test account gets locked after multiple failed attempts"""
        # Make multiple failed login attempts
        for i in range(6):  # Exceed the limit
            response = self.client.post('/accounts/login/', {
                'username': '+************',
                'password': 'wrongpassword'
            })

        # Next attempt should be blocked
        response = self.client.post('/accounts/login/', {
            'username': '+************',
            'password': 'TestPass123!'  # Even correct password should be blocked
        })

        # Should be rate limited or show lockout message
        self.assertIn(response.status_code, [423, 429, 200])  # 200 with error message

    def test_ip_based_rate_limiting(self):
        """Test IP-based rate limiting"""
        # This is tested in SecurityMiddlewareTests but worth having separate test
        cache.clear()

        # Make many requests from same IP
        for i in range(101):  # Exceed global limit
            response = self.client.get('/api/v1/accounts/login/')
            if response.status_code == 429:
                break

        # Should eventually get rate limited
        response = self.client.get('/api/v1/accounts/login/')
        self.assertEqual(response.status_code, 429)
