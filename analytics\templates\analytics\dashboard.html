{% extends 'analytics/base.html' %}

{% block title %}Analytics Dashboard{% endblock %}
{% block page_title %}Analytics Dashboard{% endblock %}

{% block content %}
<!-- Key Metrics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card card-metric h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Users</div>
                        <div class="h5 mb-0 font-weight-bold">{{ platform_summary.totals.total_registrations|default:0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card card-metric-success h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Bets</div>
                        <div class="h5 mb-0 font-weight-bold">{{ platform_summary.totals.total_bets|default:0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card card-metric-warning h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Total Revenue</div>
                        <div class="h5 mb-0 font-weight-bold">KSh {{ platform_summary.totals.total_bet_amount|default:0|floatformat:0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card metric-card card-metric-info h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Avg Active Users</div>
                        <div class="h5 mb-0 font-weight-bold">{{ platform_summary.totals.avg_active_users|default:0|floatformat:0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Daily Activity Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Daily Activity Trends</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="#" onclick="exportChart('dailyChart')">Export Chart</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="dailyChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sport Popularity Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Sport Popularity</h6>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="sportChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Tables Row -->
<div class="row">
    <!-- Recent Metrics Table -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Daily Metrics</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Active Users</th>
                                <th>New Users</th>
                                <th>Total Bets</th>
                                <th>Revenue</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for metric in recent_metrics %}
                            <tr>
                                <td>{{ metric.date }}</td>
                                <td>{{ metric.active_users }}</td>
                                <td>{{ metric.new_registrations }}</td>
                                <td>{{ metric.total_bets_placed }}</td>
                                <td>KSh {{ metric.total_bet_amount|floatformat:0 }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center">No data available</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- User Segments -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">User Segments</h6>
            </div>
            <div class="card-body">
                {% for segment in segments %}
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>{{ segment.segment_type|title }}</span>
                        <span class="badge bg-primary">{{ segment.count }}</span>
                    </div>
                    <div class="progress mt-1" style="height: 6px;">
                        <div class="progress-bar" role="progressbar" 
                             style="width: {% widthratio segment.count platform_summary.totals.total_registrations 100 %}%"></div>
                    </div>
                </div>
                {% empty %}
                <p class="text-muted">No user segments available</p>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Growth Metrics -->
{% if platform_summary.growth_metrics %}
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Growth Metrics</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="{% if platform_summary.growth_metrics.user_growth_percentage > 0 %}text-success{% else %}text-danger{% endif %}">
                                {{ platform_summary.growth_metrics.user_growth_percentage }}%
                            </h4>
                            <p class="text-muted">User Growth</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-info">{{ platform_summary.growth_metrics.first_period_avg_users }}</h4>
                            <p class="text-muted">First Period Avg</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-info">{{ platform_summary.growth_metrics.last_period_avg_users }}</h4>
                            <p class="text-muted">Last Period Avg</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// Daily Activity Chart
const dailyCtx = document.getElementById('dailyChart').getContext('2d');
fetch('{% url "analytics:api_data" %}?type=daily_metrics&days={{ days }}')
    .then(response => response.json())
    .then(data => {
        new Chart(dailyCtx, {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });

// Sport Popularity Chart
const sportCtx = document.getElementById('sportChart').getContext('2d');
fetch('{% url "analytics:api_data" %}?type=sport_popularity&days={{ days }}')
    .then(response => response.json())
    .then(data => {
        new Chart(sportCtx, {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    });

function refreshData() {
    location.reload();
}

function exportChart(chartId) {
    // Chart export functionality would go here
    alert('Chart export functionality would be implemented here');
}
</script>
{% endblock %}
