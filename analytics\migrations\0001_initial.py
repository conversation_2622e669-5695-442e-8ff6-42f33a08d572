# Generated by Django 4.2.7 on 2025-07-21 13:08

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="DailyMetrics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(db_index=True, unique=True)),
                ("new_registrations", models.PositiveIntegerField(default=0)),
                ("active_users", models.PositiveIntegerField(default=0)),
                ("returning_users", models.PositiveIntegerField(default=0)),
                ("total_bets_placed", models.PositiveIntegerField(default=0)),
                (
                    "total_bet_amount",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "total_winnings_paid",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "total_deposits",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "total_withdrawals",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "gross_gaming_revenue",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                ("page_views", models.PositiveIntegerField(default=0)),
                ("unique_visitors", models.PositiveIntegerField(default=0)),
                ("bounce_rate", models.FloatField(default=0.0)),
                ("average_session_duration", models.FloatField(default=0.0)),
                ("support_tickets_created", models.PositiveIntegerField(default=0)),
                ("support_tickets_resolved", models.PositiveIntegerField(default=0)),
                ("average_response_time", models.FloatField(default=0.0)),
                ("error_rate", models.FloatField(default=0.0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "db_table": "analytics_daily_metrics",
                "ordering": ["-date"],
            },
        ),
        migrations.CreateModel(
            name="SportPopularity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("sport", models.CharField(db_index=True, max_length=50)),
                ("league", models.CharField(blank=True, max_length=100, null=True)),
                ("total_bets", models.PositiveIntegerField(default=0)),
                (
                    "total_amount_bet",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                ("unique_bettors", models.PositiveIntegerField(default=0)),
                ("odds_views", models.PositiveIntegerField(default=0)),
                ("live_bets", models.PositiveIntegerField(default=0)),
                (
                    "date",
                    models.DateField(db_index=True, default=django.utils.timezone.now),
                ),
                ("last_updated", models.DateTimeField(auto_now=True)),
            ],
            options={
                "db_table": "analytics_sport_popularity",
                "indexes": [
                    models.Index(
                        fields=["sport", "date"], name="analytics_s_sport_5ceba6_idx"
                    ),
                    models.Index(
                        fields=["total_bets"], name="analytics_s_total_b_e178fb_idx"
                    ),
                    models.Index(
                        fields=["total_amount_bet"],
                        name="analytics_s_total_a_cf50fc_idx",
                    ),
                ],
                "unique_together": {("sport", "league", "date")},
            },
        ),
        migrations.CreateModel(
            name="RevenueReport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "report_type",
                    models.CharField(
                        choices=[
                            ("daily", "Daily Report"),
                            ("weekly", "Weekly Report"),
                            ("monthly", "Monthly Report"),
                            ("quarterly", "Quarterly Report"),
                            ("yearly", "Yearly Report"),
                        ],
                        max_length=10,
                    ),
                ),
                ("period_start", models.DateTimeField(db_index=True)),
                ("period_end", models.DateTimeField(db_index=True)),
                (
                    "gross_gaming_revenue",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "net_gaming_revenue",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "total_deposits",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "total_withdrawals",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                (
                    "payment_processing_fees",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=12
                    ),
                ),
                (
                    "operational_costs",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=12
                    ),
                ),
                (
                    "marketing_costs",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=12
                    ),
                ),
                (
                    "profit_before_tax",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=15
                    ),
                ),
                ("profit_margin", models.FloatField(default=0.0)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "db_table": "analytics_revenue_report",
                "ordering": ["-period_start"],
                "unique_together": {("report_type", "period_start", "period_end")},
            },
        ),
        migrations.CreateModel(
            name="UserSegment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "segment_type",
                    models.CharField(
                        choices=[
                            ("high_value", "High Value Customer"),
                            ("frequent_bettor", "Frequent Bettor"),
                            ("casual_bettor", "Casual Bettor"),
                            ("new_user", "New User"),
                            ("inactive", "Inactive User"),
                            ("at_risk", "At Risk User"),
                            ("vip", "VIP Customer"),
                            ("problem_gambler", "Problem Gambler"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "criteria",
                    models.JSONField(
                        default=dict, help_text="Criteria used to assign this segment"
                    ),
                ),
                (
                    "confidence_score",
                    models.FloatField(
                        default=1.0,
                        help_text="Confidence in segment assignment (0-1)",
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(1.0),
                        ],
                    ),
                ),
                ("assigned_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="segments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "analytics_user_segment",
                "indexes": [
                    models.Index(
                        fields=["segment_type", "is_active"],
                        name="analytics_u_segment_ac001f_idx",
                    ),
                    models.Index(
                        fields=["user", "is_active"],
                        name="analytics_u_user_id_d61956_idx",
                    ),
                ],
                "unique_together": {("user", "segment_type")},
            },
        ),
        migrations.CreateModel(
            name="UserActivity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_id", models.CharField(blank=True, max_length=40, null=True)),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("login", "User Login"),
                            ("logout", "User Logout"),
                            ("register", "User Registration"),
                            ("bet_placed", "Bet Placed"),
                            ("bet_won", "Bet Won"),
                            ("bet_lost", "Bet Lost"),
                            ("deposit", "Money Deposited"),
                            ("withdrawal", "Money Withdrawn"),
                            ("profile_update", "Profile Updated"),
                            ("password_change", "Password Changed"),
                            ("support_ticket", "Support Ticket Created"),
                            ("page_view", "Page View"),
                            ("search", "Search Performed"),
                            ("odds_view", "Odds Viewed"),
                            ("live_bet", "Live Bet Placed"),
                            ("jackpot_entry", "Jackpot Entry"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True, null=True)),
                ("page_url", models.URLField(blank=True, null=True)),
                ("referrer", models.URLField(blank=True, null=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "response_time",
                    models.FloatField(
                        blank=True, help_text="Response time in seconds", null=True
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="activities",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "analytics_user_activity",
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["user", "timestamp"],
                        name="analytics_u_user_id_2e1047_idx",
                    ),
                    models.Index(
                        fields=["action_type", "timestamp"],
                        name="analytics_u_action__9ccaed_idx",
                    ),
                    models.Index(
                        fields=["timestamp"], name="analytics_u_timesta_1d8413_idx"
                    ),
                    models.Index(
                        fields=["session_id"], name="analytics_u_session_7afbe9_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="BettingPattern",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("sport", models.CharField(db_index=True, max_length=50)),
                ("league", models.CharField(blank=True, max_length=100, null=True)),
                ("bet_type", models.CharField(db_index=True, max_length=20)),
                ("total_bets", models.PositiveIntegerField(default=0)),
                (
                    "total_amount_bet",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=12
                    ),
                ),
                (
                    "total_winnings",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=12
                    ),
                ),
                (
                    "win_rate",
                    models.FloatField(
                        default=0.0,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(100.0),
                        ],
                    ),
                ),
                (
                    "average_stake",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                ("preferred_odds_range_min", models.FloatField(default=1.0)),
                ("preferred_odds_range_max", models.FloatField(default=10.0)),
                (
                    "most_active_hour",
                    models.PositiveSmallIntegerField(blank=True, null=True),
                ),
                (
                    "most_active_day",
                    models.PositiveSmallIntegerField(blank=True, null=True),
                ),
                ("last_updated", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="betting_patterns",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "analytics_betting_pattern",
                "indexes": [
                    models.Index(
                        fields=["user", "sport"], name="analytics_b_user_id_6c26ca_idx"
                    ),
                    models.Index(
                        fields=["sport", "bet_type"],
                        name="analytics_b_sport_d50769_idx",
                    ),
                    models.Index(
                        fields=["win_rate"], name="analytics_b_win_rat_a4c9d0_idx"
                    ),
                ],
                "unique_together": {("user", "sport", "bet_type")},
            },
        ),
    ]
