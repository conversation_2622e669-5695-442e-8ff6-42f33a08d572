"""
Management command to update daily metrics
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from datetime import datetime, timedelta

from analytics.utils import calculate_daily_metrics


class Command(BaseCommand):
    help = 'Update daily analytics metrics'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--date',
            type=str,
            help='Specific date to update (YYYY-MM-DD format)'
        )
        
        parser.add_argument(
            '--days',
            type=int,
            default=1,
            help='Number of days to update (from today backwards)'
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update even if metrics already exist'
        )
    
    def handle(self, *args, **options):
        specific_date = options.get('date')
        days = options['days']
        force_update = options['force']
        
        if specific_date:
            # Update specific date
            try:
                date = datetime.strptime(specific_date, '%Y-%m-%d').date()
                self._update_metrics_for_date(date, force_update)
            except ValueError:
                raise CommandError('Invalid date format. Use YYYY-MM-DD')
        else:
            # Update multiple days
            end_date = timezone.now().date()
            for i in range(days):
                date = end_date - timedelta(days=i)
                self._update_metrics_for_date(date, force_update)
        
        self.stdout.write(
            self.style.SUCCESS('Daily metrics updated successfully!')
        )
    
    def _update_metrics_for_date(self, date, force_update):
        """Update metrics for a specific date"""
        from analytics.models import DailyMetrics
        
        # Check if metrics already exist
        if not force_update and DailyMetrics.objects.filter(date=date).exists():
            self.stdout.write(
                self.style.WARNING(f'Metrics for {date} already exist. Use --force to update.')
            )
            return
        
        self.stdout.write(f'Updating metrics for {date}...')
        
        try:
            metrics = calculate_daily_metrics(date)
            self.stdout.write(
                self.style.SUCCESS(f'✓ Updated metrics for {date}')
            )
            
            # Display summary
            self.stdout.write(f'  - Active Users: {metrics.active_users}')
            self.stdout.write(f'  - New Registrations: {metrics.new_registrations}')
            self.stdout.write(f'  - Total Bets: {metrics.total_bets_placed}')
            self.stdout.write(f'  - Revenue: KSh {metrics.total_bet_amount:,.0f}')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ Failed to update metrics for {date}: {str(e)}')
            )
