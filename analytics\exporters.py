"""
Data export utilities for analytics
"""

import csv
import json
import io
from datetime import datetime
from typing import Dict, List, Any, Optional
from django.http import HttpResponse
from django.utils import timezone
from django.db.models import QuerySet
from django.core.serializers import serialize

from .models import UserActivity, BettingPattern, DailyMetrics, SportPopularity, RevenueReport


class BaseExporter:
    """Base class for data exporters"""
    
    def __init__(self, queryset: QuerySet, filename: str = None):
        self.queryset = queryset
        self.filename = filename or f"export_{timezone.now().strftime('%Y%m%d_%H%M%S')}"
    
    def export(self) -> HttpResponse:
        """Export data and return HTTP response"""
        raise NotImplementedError("Subclasses must implement export method")
    
    def _get_content_type(self) -> str:
        """Get content type for HTTP response"""
        raise NotImplementedError("Subclasses must implement _get_content_type method")
    
    def _get_file_extension(self) -> str:
        """Get file extension"""
        raise NotImplementedError("Subclasses must implement _get_file_extension method")


class JSONExporter(BaseExporter):
    """Export data as JSON"""
    
    def export(self) -> HttpResponse:
        data = []
        
        for item in self.queryset:
            if hasattr(item, 'to_dict'):
                data.append(item.to_dict())
            else:
                # Convert model instance to dict
                item_dict = {}
                for field in item._meta.fields:
                    value = getattr(item, field.name)
                    if isinstance(value, datetime):
                        value = value.isoformat()
                    elif hasattr(value, '__str__'):
                        value = str(value)
                    item_dict[field.name] = value
                data.append(item_dict)
        
        export_data = {
            'data': data,
            'count': len(data),
            'exported_at': timezone.now().isoformat(),
            'model': self.queryset.model.__name__
        }
        
        response = HttpResponse(
            json.dumps(export_data, indent=2, default=str),
            content_type=self._get_content_type()
        )
        response['Content-Disposition'] = f'attachment; filename="{self.filename}.{self._get_file_extension()}"'
        return response
    
    def _get_content_type(self) -> str:
        return 'application/json'
    
    def _get_file_extension(self) -> str:
        return 'json'


class CSVExporter(BaseExporter):
    """Export data as CSV"""
    
    def __init__(self, queryset: QuerySet, filename: str = None, fields: List[str] = None):
        super().__init__(queryset, filename)
        self.fields = fields
    
    def export(self) -> HttpResponse:
        output = io.StringIO()
        
        # Get field names
        if self.fields:
            fieldnames = self.fields
        else:
            fieldnames = [field.name for field in self.queryset.model._meta.fields]
        
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        
        for item in self.queryset:
            row = {}
            for field_name in fieldnames:
                value = getattr(item, field_name, '')
                if isinstance(value, datetime):
                    value = value.strftime('%Y-%m-%d %H:%M:%S')
                elif value is None:
                    value = ''
                else:
                    value = str(value)
                row[field_name] = value
            writer.writerow(row)
        
        response = HttpResponse(
            output.getvalue(),
            content_type=self._get_content_type()
        )
        response['Content-Disposition'] = f'attachment; filename="{self.filename}.{self._get_file_extension()}"'
        return response
    
    def _get_content_type(self) -> str:
        return 'text/csv'
    
    def _get_file_extension(self) -> str:
        return 'csv'


class ExcelExporter(BaseExporter):
    """Export data as Excel file"""
    
    def __init__(self, queryset: QuerySet, filename: str = None, sheet_name: str = 'Data'):
        super().__init__(queryset, filename)
        self.sheet_name = sheet_name
    
    def export(self) -> HttpResponse:
        try:
            import openpyxl
            from openpyxl.utils.dataframe import dataframe_to_rows
            import pandas as pd
        except ImportError:
            raise ImportError("openpyxl and pandas are required for Excel export")
        
        # Convert queryset to DataFrame
        data = []
        for item in self.queryset:
            row = {}
            for field in item._meta.fields:
                value = getattr(item, field.name)
                if isinstance(value, datetime):
                    value = value.strftime('%Y-%m-%d %H:%M:%S')
                row[field.verbose_name or field.name] = value
            data.append(row)
        
        df = pd.DataFrame(data)
        
        # Create Excel workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = self.sheet_name
        
        # Add data to worksheet
        for r in dataframe_to_rows(df, index=False, header=True):
            ws.append(r)
        
        # Save to BytesIO
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        
        response = HttpResponse(
            output.getvalue(),
            content_type=self._get_content_type()
        )
        response['Content-Disposition'] = f'attachment; filename="{self.filename}.{self._get_file_extension()}"'
        return response
    
    def _get_content_type(self) -> str:
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    
    def _get_file_extension(self) -> str:
        return 'xlsx'


class AnalyticsExporter:
    """Main analytics data exporter"""
    
    EXPORTERS = {
        'json': JSONExporter,
        'csv': CSVExporter,
        'excel': ExcelExporter,
    }
    
    @classmethod
    def export_user_activities(cls, start_date, end_date, format='json', user_id=None):
        """Export user activities"""
        queryset = UserActivity.objects.filter(
            timestamp__range=(start_date, end_date)
        ).select_related('user')
        
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        filename = f"user_activities_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}"
        
        if format == 'csv':
            fields = ['user__phone_number', 'action_type', 'timestamp', 'ip_address', 'page_url']
            exporter = cls.EXPORTERS[format](queryset, filename, fields)
        else:
            exporter = cls.EXPORTERS[format](queryset, filename)
        
        return exporter.export()
    
    @classmethod
    def export_betting_patterns(cls, format='json', sport=None):
        """Export betting patterns"""
        queryset = BettingPattern.objects.select_related('user')
        
        if sport:
            queryset = queryset.filter(sport=sport)
        
        filename = f"betting_patterns_{timezone.now().strftime('%Y%m%d')}"
        if sport:
            filename += f"_{sport}"
        
        if format == 'csv':
            fields = ['user__phone_number', 'sport', 'bet_type', 'total_bets', 
                     'total_amount_bet', 'total_winnings', 'win_rate', 'average_stake']
            exporter = cls.EXPORTERS[format](queryset, filename, fields)
        else:
            exporter = cls.EXPORTERS[format](queryset, filename)
        
        return exporter.export()
    
    @classmethod
    def export_daily_metrics(cls, start_date, end_date, format='json'):
        """Export daily metrics"""
        queryset = DailyMetrics.objects.filter(
            date__range=(start_date.date(), end_date.date())
        ).order_by('date')
        
        filename = f"daily_metrics_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}"
        
        exporter = cls.EXPORTERS[format](queryset, filename)
        return exporter.export()
    
    @classmethod
    def export_revenue_reports(cls, report_type='monthly', format='json'):
        """Export revenue reports"""
        queryset = RevenueReport.objects.filter(report_type=report_type).order_by('-period_start')
        
        filename = f"revenue_reports_{report_type}_{timezone.now().strftime('%Y%m%d')}"
        
        exporter = cls.EXPORTERS[format](queryset, filename)
        return exporter.export()
    
    @classmethod
    def export_sport_popularity(cls, start_date, end_date, format='json'):
        """Export sport popularity data"""
        queryset = SportPopularity.objects.filter(
            date__range=(start_date.date(), end_date.date())
        ).order_by('-total_bets')
        
        filename = f"sport_popularity_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}"
        
        exporter = cls.EXPORTERS[format](queryset, filename)
        return exporter.export()
    
    @classmethod
    def export_custom_report(cls, data: List[Dict], filename: str, format='json'):
        """Export custom report data"""
        if format == 'json':
            export_data = {
                'data': data,
                'count': len(data),
                'exported_at': timezone.now().isoformat(),
                'type': 'custom_report'
            }
            
            response = HttpResponse(
                json.dumps(export_data, indent=2, default=str),
                content_type='application/json'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}.json"'
            return response
        
        elif format == 'csv':
            output = io.StringIO()
            
            if data:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(output, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
            
            response = HttpResponse(
                output.getvalue(),
                content_type='text/csv'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}.csv"'
            return response
        
        else:
            raise ValueError(f"Unsupported format: {format}")


def get_available_export_formats():
    """Get list of available export formats"""
    return list(AnalyticsExporter.EXPORTERS.keys())


def validate_export_format(format_name: str) -> bool:
    """Validate if export format is supported"""
    return format_name in AnalyticsExporter.EXPORTERS
