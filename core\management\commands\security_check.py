"""
Management command to run security checks
"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.db import connection
import os
import sys
from typing import List, Tuple

User = get_user_model()


class Command(BaseCommand):
    help = 'Run comprehensive security checks on the application'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='Attempt to fix security issues where possible',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output',
        )
    
    def handle(self, *args, **options):
        self.verbose = options['verbose']
        self.fix_issues = options['fix']
        
        self.stdout.write(
            self.style.SUCCESS('Running security checks...\n')
        )
        
        issues = []
        
        # Run all security checks
        issues.extend(self.check_django_settings())
        issues.extend(self.check_database_security())
        issues.extend(self.check_user_security())
        issues.extend(self.check_file_permissions())
        issues.extend(self.check_dependencies())
        issues.extend(self.check_middleware_configuration())
        issues.extend(self.check_encryption_setup())
        
        # Report results
        self.report_results(issues)
    
    def check_django_settings(self) -> List[Tuple[str, str, str]]:
        """Check Django security settings"""
        issues = []
        
        # Check DEBUG setting
        if settings.DEBUG:
            issues.append((
                'HIGH',
                'DEBUG is enabled',
                'Set DEBUG = False in production'
            ))
        
        # Check SECRET_KEY
        if len(settings.SECRET_KEY) < 50:
            issues.append((
                'HIGH',
                'SECRET_KEY is too short',
                'Use a longer, more complex SECRET_KEY'
            ))
        
        if settings.SECRET_KEY == 'django-insecure-tn5a5#e4%nd)zk5ug0^x$r833c(8#2n)eqf%7%&56_8gktq&@9':
            issues.append((
                'CRITICAL',
                'Using default SECRET_KEY',
                'Generate a new SECRET_KEY immediately'
            ))
        
        # Check ALLOWED_HOSTS
        if not settings.ALLOWED_HOSTS or '*' in settings.ALLOWED_HOSTS:
            issues.append((
                'MEDIUM',
                'ALLOWED_HOSTS not properly configured',
                'Set specific allowed hosts'
            ))
        
        # Check security middleware
        required_middleware = [
            'django.middleware.security.SecurityMiddleware',
            'django.middleware.csrf.CsrfViewMiddleware',
            'django.contrib.auth.middleware.AuthenticationMiddleware',
            'core.middleware.SecurityHeadersMiddleware',
            'core.middleware.RateLimitMiddleware',
        ]
        
        for middleware in required_middleware:
            if middleware not in settings.MIDDLEWARE:
                issues.append((
                    'MEDIUM',
                    f'Missing security middleware: {middleware}',
                    f'Add {middleware} to MIDDLEWARE setting'
                ))
        
        # Check session security
        if not getattr(settings, 'SESSION_COOKIE_SECURE', False) and not settings.DEBUG:
            issues.append((
                'MEDIUM',
                'SESSION_COOKIE_SECURE not enabled',
                'Set SESSION_COOKIE_SECURE = True for HTTPS'
            ))
        
        if not getattr(settings, 'CSRF_COOKIE_SECURE', False) and not settings.DEBUG:
            issues.append((
                'MEDIUM',
                'CSRF_COOKIE_SECURE not enabled',
                'Set CSRF_COOKIE_SECURE = True for HTTPS'
            ))
        
        return issues
    
    def check_database_security(self) -> List[Tuple[str, str, str]]:
        """Check database security configuration"""
        issues = []
        
        # Check database connection
        db_settings = settings.DATABASES['default']
        
        # Check for default passwords
        if db_settings.get('PASSWORD') in ['', 'password', '123456', 'admin']:
            issues.append((
                'HIGH',
                'Weak database password',
                'Use a strong database password'
            ))
        
        # Check SSL usage
        if not db_settings.get('OPTIONS', {}).get('sslmode'):
            issues.append((
                'MEDIUM',
                'Database SSL not configured',
                'Enable SSL for database connections'
            ))
        
        return issues
    
    def check_user_security(self) -> List[Tuple[str, str, str]]:
        """Check user account security"""
        issues = []
        
        # Check for users with weak passwords
        weak_password_users = 0
        superuser_count = User.objects.filter(is_superuser=True).count()
        
        if superuser_count > 5:
            issues.append((
                'MEDIUM',
                f'Too many superusers ({superuser_count})',
                'Limit the number of superuser accounts'
            ))
        
        # Check for inactive superusers
        inactive_superusers = User.objects.filter(
            is_superuser=True,
            is_active=False
        ).count()
        
        if inactive_superusers > 0:
            issues.append((
                'LOW',
                f'{inactive_superusers} inactive superuser accounts',
                'Remove or clean up inactive superuser accounts'
            ))
        
        return issues
    
    def check_file_permissions(self) -> List[Tuple[str, str, str]]:
        """Check file and directory permissions"""
        issues = []
        
        # Check settings file permissions
        settings_file = os.path.join(settings.BASE_DIR, 'betika_clone', 'settings.py')
        if os.path.exists(settings_file):
            stat_info = os.stat(settings_file)
            permissions = oct(stat_info.st_mode)[-3:]
            
            if permissions != '644':
                issues.append((
                    'MEDIUM',
                    f'Settings file has permissions {permissions}',
                    'Set settings file permissions to 644'
                ))
        
        # Check media directory permissions
        if hasattr(settings, 'MEDIA_ROOT') and os.path.exists(settings.MEDIA_ROOT):
            stat_info = os.stat(settings.MEDIA_ROOT)
            permissions = oct(stat_info.st_mode)[-3:]
            
            if permissions == '777':
                issues.append((
                    'HIGH',
                    'Media directory has 777 permissions',
                    'Restrict media directory permissions'
                ))
        
        return issues
    
    def check_dependencies(self) -> List[Tuple[str, str, str]]:
        """Check for security issues in dependencies"""
        issues = []
        
        # This would integrate with safety or other security scanners
        # For now, just check if requirements.txt exists
        requirements_file = os.path.join(settings.BASE_DIR, 'requirements.txt')
        if not os.path.exists(requirements_file):
            issues.append((
                'LOW',
                'No requirements.txt file found',
                'Create requirements.txt to track dependencies'
            ))
        
        return issues
    
    def check_middleware_configuration(self) -> List[Tuple[str, str, str]]:
        """Check middleware configuration"""
        issues = []
        
        # Check rate limiting configuration
        if not hasattr(settings, 'RATE_LIMITS'):
            issues.append((
                'MEDIUM',
                'Rate limiting not configured',
                'Configure RATE_LIMITS setting'
            ))
        
        # Check session timeout
        if not hasattr(settings, 'SESSION_TIMEOUT'):
            issues.append((
                'LOW',
                'Session timeout not configured',
                'Configure SESSION_TIMEOUT setting'
            ))
        
        return issues
    
    def check_encryption_setup(self) -> List[Tuple[str, str, str]]:
        """Check encryption configuration"""
        issues = []
        
        # Check if encryption key is configured
        if not hasattr(settings, 'FIELD_ENCRYPTION_KEY'):
            issues.append((
                'MEDIUM',
                'Field encryption key not configured',
                'Set FIELD_ENCRYPTION_KEY for sensitive data encryption'
            ))
        
        # Test encryption functionality
        try:
            from core.encryption import field_encryption
            test_value = "test"
            encrypted = field_encryption.encrypt(test_value)
            decrypted = field_encryption.decrypt(encrypted)
            
            if decrypted != test_value:
                issues.append((
                    'HIGH',
                    'Encryption/decryption not working properly',
                    'Check encryption configuration'
                ))
        except Exception as e:
            issues.append((
                'HIGH',
                f'Encryption system error: {str(e)}',
                'Fix encryption configuration'
            ))
        
        return issues
    
    def report_results(self, issues: List[Tuple[str, str, str]]):
        """Report security check results"""
        if not issues:
            self.stdout.write(
                self.style.SUCCESS('✓ No security issues found!')
            )
            return
        
        # Group issues by severity
        critical = [i for i in issues if i[0] == 'CRITICAL']
        high = [i for i in issues if i[0] == 'HIGH']
        medium = [i for i in issues if i[0] == 'MEDIUM']
        low = [i for i in issues if i[0] == 'LOW']
        
        # Report critical issues
        if critical:
            self.stdout.write(
                self.style.ERROR(f'\n🚨 CRITICAL ISSUES ({len(critical)}):')
            )
            for _, issue, fix in critical:
                self.stdout.write(f'  ❌ {issue}')
                if self.verbose:
                    self.stdout.write(f'     Fix: {fix}')
        
        # Report high severity issues
        if high:
            self.stdout.write(
                self.style.ERROR(f'\n⚠️  HIGH SEVERITY ({len(high)}):')
            )
            for _, issue, fix in high:
                self.stdout.write(f'  ❌ {issue}')
                if self.verbose:
                    self.stdout.write(f'     Fix: {fix}')
        
        # Report medium severity issues
        if medium:
            self.stdout.write(
                self.style.WARNING(f'\n⚠️  MEDIUM SEVERITY ({len(medium)}):')
            )
            for _, issue, fix in medium:
                self.stdout.write(f'  ⚠️  {issue}')
                if self.verbose:
                    self.stdout.write(f'     Fix: {fix}')
        
        # Report low severity issues
        if low:
            self.stdout.write(
                self.style.NOTICE(f'\n💡 LOW SEVERITY ({len(low)}):')
            )
            for _, issue, fix in low:
                self.stdout.write(f'  💡 {issue}')
                if self.verbose:
                    self.stdout.write(f'     Fix: {fix}')
        
        # Summary
        total_issues = len(issues)
        self.stdout.write(
            f'\n📊 SUMMARY: {total_issues} security issues found'
        )
        
        if critical or high:
            self.stdout.write(
                self.style.ERROR('❌ Action required: Critical or high severity issues found')
            )
            sys.exit(1)
        elif medium:
            self.stdout.write(
                self.style.WARNING('⚠️  Recommendation: Address medium severity issues')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('✓ No critical security issues found')
            )
