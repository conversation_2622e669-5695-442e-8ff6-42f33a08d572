"""
Secure forms with file upload handling
"""

from django import forms
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import UploadedFile
from .file_security import SecureFileUploadHandler, validate_uploaded_file
from .validators import In<PERSON><PERSON><PERSON><PERSON><PERSON>, InputValidator


class SecureFileUploadForm(forms.Form):
    """
    Base form for secure file uploads
    """
    file = forms.FileField(
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.jpg,.jpeg,.png,.pdf,.doc,.docx'
        })
    )
    
    def __init__(self, file_category='documents', *args, **kwargs):
        self.file_category = file_category
        super().__init__(*args, **kwargs)
        
        # Update accept attribute based on file category
        handler = SecureFileUploadHandler(file_category)
        extensions = handler.config['extensions']
        accept_attr = ','.join([f'.{ext}' for ext in extensions])
        self.fields['file'].widget.attrs['accept'] = accept_attr
    
    def clean_file(self):
        uploaded_file = self.cleaned_data.get('file')
        
        if uploaded_file:
            # Validate file using secure handler
            is_valid, error_message = validate_uploaded_file(uploaded_file, self.file_category)
            
            if not is_valid:
                raise ValidationError(error_message)
        
        return uploaded_file


class ProfileImageUploadForm(SecureFileUploadForm):
    """
    Form for uploading profile images
    """
    def __init__(self, *args, **kwargs):
        super().__init__(file_category='profile_images', *args, **kwargs)
        
        self.fields['file'].label = 'Profile Image'
        self.fields['file'].help_text = 'Upload a profile image (JPG, PNG only, max 2MB)'
        self.fields['file'].widget.attrs.update({
            'accept': '.jpg,.jpeg,.png',
            'class': 'form-control'
        })


class DocumentUploadForm(SecureFileUploadForm):
    """
    Form for uploading documents
    """
    document_type = forms.ChoiceField(
        choices=[
            ('id', 'ID Document'),
            ('proof_of_address', 'Proof of Address'),
            ('bank_statement', 'Bank Statement'),
            ('other', 'Other')
        ],
        widget=forms.Select(attrs={
            'class': 'form-control'
        })
    )
    
    description = forms.CharField(
        max_length=500,
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Optional description'
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(file_category='documents', *args, **kwargs)
        
        self.fields['file'].label = 'Document'
        self.fields['file'].help_text = 'Upload a document (PDF, DOC, DOCX, max 10MB)'
    
    def clean_description(self):
        description = self.cleaned_data.get('description')
        if description:
            return InputSanitizer.sanitize_text(description)
        return description


class BulkFileUploadForm(forms.Form):
    """
    Form for uploading multiple files
    """
    files = forms.FileField(
        widget=forms.ClearableFileInput(attrs={
            'multiple': True,
            'class': 'form-control'
        })
    )
    
    def __init__(self, file_category='documents', max_files=5, *args, **kwargs):
        self.file_category = file_category
        self.max_files = max_files
        super().__init__(*args, **kwargs)
    
    def clean_files(self):
        files = self.files.getlist('files')
        
        if not files:
            raise ValidationError("At least one file is required")
        
        if len(files) > self.max_files:
            raise ValidationError(f"Maximum {self.max_files} files allowed")
        
        # Validate each file
        for uploaded_file in files:
            is_valid, error_message = validate_uploaded_file(uploaded_file, self.file_category)
            if not is_valid:
                raise ValidationError(f"File '{uploaded_file.name}': {error_message}")
        
        return files


class SecureContactForm(forms.Form):
    """
    Secure contact form with file attachment support
    """
    name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Your name'
        })
    )
    
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )
    
    subject = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Subject'
        })
    )
    
    message = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 5,
            'placeholder': 'Your message'
        })
    )
    
    attachment = forms.FileField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.jpg,.jpeg,.png,.pdf,.doc,.docx'
        })
    )
    
    def clean_name(self):
        name = self.cleaned_data.get('name')
        return InputSanitizer.sanitize_text(name)
    
    def clean_email(self):
        email = self.cleaned_data.get('email')
        sanitized_email = InputSanitizer.sanitize_text(email).lower()
        
        if not InputValidator.validate_email_address(sanitized_email):
            raise ValidationError("Please enter a valid email address")
        
        return sanitized_email
    
    def clean_subject(self):
        subject = self.cleaned_data.get('subject')
        return InputSanitizer.sanitize_text(subject)
    
    def clean_message(self):
        message = self.cleaned_data.get('message')
        return InputSanitizer.sanitize_html(message)
    
    def clean_attachment(self):
        attachment = self.cleaned_data.get('attachment')
        
        if attachment:
            is_valid, error_message = validate_uploaded_file(attachment, 'documents')
            if not is_valid:
                raise ValidationError(error_message)
        
        return attachment


class KYCDocumentUploadForm(forms.Form):
    """
    Form for KYC document uploads
    """
    DOCUMENT_TYPES = [
        ('national_id', 'National ID'),
        ('passport', 'Passport'),
        ('driving_license', 'Driving License'),
        ('utility_bill', 'Utility Bill'),
        ('bank_statement', 'Bank Statement'),
    ]
    
    document_type = forms.ChoiceField(
        choices=DOCUMENT_TYPES,
        widget=forms.Select(attrs={
            'class': 'form-control'
        })
    )
    
    front_image = forms.FileField(
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.jpg,.jpeg,.png'
        }),
        help_text='Front side of the document'
    )
    
    back_image = forms.FileField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': '.jpg,.jpeg,.png'
        }),
        help_text='Back side of the document (if applicable)'
    )
    
    def clean_front_image(self):
        front_image = self.cleaned_data.get('front_image')
        
        if front_image:
            is_valid, error_message = validate_uploaded_file(front_image, 'images')
            if not is_valid:
                raise ValidationError(f"Front image: {error_message}")
        
        return front_image
    
    def clean_back_image(self):
        back_image = self.cleaned_data.get('back_image')
        
        if back_image:
            is_valid, error_message = validate_uploaded_file(back_image, 'images')
            if not is_valid:
                raise ValidationError(f"Back image: {error_message}")
        
        return back_image


def validate_file_upload_data(data, files):
    """
    Validate file upload data using the validation framework
    """
    from .validators import validate_form_data
    
    validation_rules = {
        'document_type': {
            'required': True,
            'pattern': r'^[a-z_]+$',
            'pattern_error': 'Invalid document type'
        },
        'description': {
            'required': False,
            'max_length': 500
        }
    }
    
    errors = validate_form_data(data, validation_rules)
    
    # Validate files
    if 'file' in files:
        uploaded_file = files['file']
        is_valid, error_message = validate_uploaded_file(uploaded_file)
        if not is_valid:
            errors['file'] = [error_message]
    
    return errors
