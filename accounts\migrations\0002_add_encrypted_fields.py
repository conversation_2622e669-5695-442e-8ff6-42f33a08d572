# Generated migration for adding encrypted fields

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='customuser',
            name='phone_number_encrypted',
            field=models.TextField(blank=True, null=True, help_text='Encrypted phone number'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='phone_number_hash',
            field=models.CharField(max_length=64, blank=True, null=True, help_text='Hash for searching encrypted phone number', db_index=True),
        ),
        migrations.AddField(
            model_name='customuser',
            name='email_encrypted',
            field=models.TextField(blank=True, null=True, help_text='Encrypted email address'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='email_hash',
            field=models.CharField(max_length=64, blank=True, null=True, help_text='Hash for searching encrypted email', db_index=True),
        ),
        migrations.AddField(
            model_name='customuser',
            name='first_name_encrypted',
            field=models.TextField(blank=True, null=True, help_text='Encrypted first name'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='last_name_encrypted',
            field=models.TextField(blank=True, null=True, help_text='Encrypted last name'),
        ),
    ]
