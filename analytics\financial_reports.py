"""
Financial reporting and transaction analysis utilities
"""

from django.db.models import Sum, Count, Avg, Q, F
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
import json

from .models import DailyMetrics, RevenueReport, UserActivity

User = get_user_model()


class TransactionReporter:
    """
    Comprehensive transaction reporting system
    """
    
    def __init__(self, start_date=None, end_date=None):
        self.start_date = start_date or (timezone.now() - timedelta(days=30))
        self.end_date = end_date or timezone.now()
    
    def get_transaction_summary(self) -> Dict:
        """Get overall transaction summary"""
        # Import here to avoid circular imports
        try:
            from payments.models import Transaction
        except ImportError:
            # Fallback if payments app not available
            return self._get_activity_based_summary()
        
        transactions = Transaction.objects.filter(
            created_at__range=(self.start_date, self.end_date)
        )
        
        # Group by transaction type
        deposits = transactions.filter(transaction_type='deposit', status='completed')
        withdrawals = transactions.filter(transaction_type='withdrawal', status='completed')
        
        # Calculate totals
        total_deposits = deposits.aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        total_withdrawals = withdrawals.aggregate(total=Sum('amount'))['total'] or Decimal('0.00')
        
        # Count transactions
        deposit_count = deposits.count()
        withdrawal_count = withdrawals.count()
        
        # Calculate averages
        avg_deposit = deposits.aggregate(avg=Avg('amount'))['avg'] or Decimal('0.00')
        avg_withdrawal = withdrawals.aggregate(avg=Avg('amount'))['avg'] or Decimal('0.00')
        
        # Payment method breakdown
        payment_methods = transactions.values('payment_method').annotate(
            count=Count('id'),
            total_amount=Sum('amount')
        ).order_by('-total_amount')
        
        return {
            'period': {
                'start': self.start_date.date(),
                'end': self.end_date.date(),
                'days': (self.end_date.date() - self.start_date.date()).days
            },
            'deposits': {
                'total_amount': float(total_deposits),
                'count': deposit_count,
                'average_amount': float(avg_deposit),
            },
            'withdrawals': {
                'total_amount': float(total_withdrawals),
                'count': withdrawal_count,
                'average_amount': float(avg_withdrawal),
            },
            'net_cash_flow': float(total_deposits - total_withdrawals),
            'payment_methods': list(payment_methods),
            'transaction_volume': deposit_count + withdrawal_count,
        }
    
    def _get_activity_based_summary(self) -> Dict:
        """Fallback method using UserActivity data"""
        deposit_activities = UserActivity.objects.filter(
            timestamp__range=(self.start_date, self.end_date),
            action_type='deposit'
        )
        
        withdrawal_activities = UserActivity.objects.filter(
            timestamp__range=(self.start_date, self.end_date),
            action_type='withdrawal'
        )
        
        # Extract amounts from metadata
        total_deposits = Decimal('0.00')
        total_withdrawals = Decimal('0.00')
        
        for activity in deposit_activities:
            if 'amount' in activity.metadata:
                try:
                    total_deposits += Decimal(str(activity.metadata['amount']))
                except (ValueError, TypeError):
                    pass
        
        for activity in withdrawal_activities:
            if 'amount' in activity.metadata:
                try:
                    total_withdrawals += Decimal(str(activity.metadata['amount']))
                except (ValueError, TypeError):
                    pass
        
        return {
            'period': {
                'start': self.start_date.date(),
                'end': self.end_date.date(),
                'days': (self.end_date.date() - self.start_date.date()).days
            },
            'deposits': {
                'total_amount': float(total_deposits),
                'count': deposit_activities.count(),
                'average_amount': float(total_deposits / deposit_activities.count()) if deposit_activities.count() > 0 else 0,
            },
            'withdrawals': {
                'total_amount': float(total_withdrawals),
                'count': withdrawal_activities.count(),
                'average_amount': float(total_withdrawals / withdrawal_activities.count()) if withdrawal_activities.count() > 0 else 0,
            },
            'net_cash_flow': float(total_deposits - total_withdrawals),
            'payment_methods': [],
            'transaction_volume': deposit_activities.count() + withdrawal_activities.count(),
        }
    
    def get_daily_transaction_breakdown(self) -> List[Dict]:
        """Get daily transaction breakdown"""
        try:
            from payments.models import Transaction
        except ImportError:
            return self._get_daily_activity_breakdown()
        
        # Get daily metrics
        daily_data = []
        current_date = self.start_date.date()
        
        while current_date <= self.end_date.date():
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())
            
            day_transactions = Transaction.objects.filter(
                created_at__range=(day_start, day_end),
                status='completed'
            )
            
            deposits = day_transactions.filter(transaction_type='deposit')
            withdrawals = day_transactions.filter(transaction_type='withdrawal')
            
            daily_data.append({
                'date': str(current_date),
                'deposits': {
                    'count': deposits.count(),
                    'amount': float(deposits.aggregate(total=Sum('amount'))['total'] or Decimal('0.00'))
                },
                'withdrawals': {
                    'count': withdrawals.count(),
                    'amount': float(withdrawals.aggregate(total=Sum('amount'))['total'] or Decimal('0.00'))
                },
                'net_flow': float(
                    (deposits.aggregate(total=Sum('amount'))['total'] or Decimal('0.00')) -
                    (withdrawals.aggregate(total=Sum('amount'))['total'] or Decimal('0.00'))
                )
            })
            
            current_date += timedelta(days=1)
        
        return daily_data
    
    def _get_daily_activity_breakdown(self) -> List[Dict]:
        """Fallback daily breakdown using activities"""
        daily_data = []
        current_date = self.start_date.date()
        
        while current_date <= self.end_date.date():
            day_start = datetime.combine(current_date, datetime.min.time())
            day_end = datetime.combine(current_date, datetime.max.time())
            
            deposits = UserActivity.objects.filter(
                timestamp__range=(day_start, day_end),
                action_type='deposit'
            )
            
            withdrawals = UserActivity.objects.filter(
                timestamp__range=(day_start, day_end),
                action_type='withdrawal'
            )
            
            daily_data.append({
                'date': str(current_date),
                'deposits': {
                    'count': deposits.count(),
                    'amount': 0  # Would need to extract from metadata
                },
                'withdrawals': {
                    'count': withdrawals.count(),
                    'amount': 0  # Would need to extract from metadata
                },
                'net_flow': 0
            })
            
            current_date += timedelta(days=1)
        
        return daily_data
    
    def get_user_transaction_analysis(self, limit=50) -> List[Dict]:
        """Get top users by transaction volume"""
        try:
            from payments.models import Transaction
        except ImportError:
            return self._get_user_activity_analysis(limit)
        
        user_stats = Transaction.objects.filter(
            created_at__range=(self.start_date, self.end_date),
            status='completed'
        ).values('user').annotate(
            total_transactions=Count('id'),
            total_amount=Sum('amount'),
            deposits=Count('id', filter=Q(transaction_type='deposit')),
            withdrawals=Count('id', filter=Q(transaction_type='withdrawal')),
            deposit_amount=Sum('amount', filter=Q(transaction_type='deposit')),
            withdrawal_amount=Sum('amount', filter=Q(transaction_type='withdrawal'))
        ).order_by('-total_amount')[:limit]
        
        # Enrich with user data
        user_analysis = []
        for stat in user_stats:
            try:
                user = User.objects.get(id=stat['user'])
                user_analysis.append({
                    'user_id': user.id,
                    'phone_number': user.phone_number,
                    'total_transactions': stat['total_transactions'],
                    'total_amount': float(stat['total_amount'] or 0),
                    'deposits': stat['deposits'],
                    'withdrawals': stat['withdrawals'],
                    'deposit_amount': float(stat['deposit_amount'] or 0),
                    'withdrawal_amount': float(stat['withdrawal_amount'] or 0),
                    'net_position': float((stat['deposit_amount'] or 0) - (stat['withdrawal_amount'] or 0))
                })
            except User.DoesNotExist:
                continue
        
        return user_analysis
    
    def _get_user_activity_analysis(self, limit=50) -> List[Dict]:
        """Fallback user analysis using activities"""
        user_stats = UserActivity.objects.filter(
            timestamp__range=(self.start_date, self.end_date),
            action_type__in=['deposit', 'withdrawal'],
            user__isnull=False
        ).values('user').annotate(
            total_transactions=Count('id'),
            deposits=Count('id', filter=Q(action_type='deposit')),
            withdrawals=Count('id', filter=Q(action_type='withdrawal'))
        ).order_by('-total_transactions')[:limit]
        
        user_analysis = []
        for stat in user_stats:
            try:
                user = User.objects.get(id=stat['user'])
                user_analysis.append({
                    'user_id': user.id,
                    'phone_number': user.phone_number,
                    'total_transactions': stat['total_transactions'],
                    'total_amount': 0,  # Would need metadata extraction
                    'deposits': stat['deposits'],
                    'withdrawals': stat['withdrawals'],
                    'deposit_amount': 0,
                    'withdrawal_amount': 0,
                    'net_position': 0
                })
            except User.DoesNotExist:
                continue
        
        return user_analysis
    
    def get_transaction_trends(self) -> Dict:
        """Get transaction trends and patterns"""
        # Weekly trends
        weekly_data = []
        current_week_start = self.start_date.date()
        
        while current_week_start <= self.end_date.date():
            week_end = min(current_week_start + timedelta(days=6), self.end_date.date())
            
            try:
                from payments.models import Transaction
                week_transactions = Transaction.objects.filter(
                    created_at__date__range=(current_week_start, week_end),
                    status='completed'
                )
                
                weekly_data.append({
                    'week_start': str(current_week_start),
                    'week_end': str(week_end),
                    'transaction_count': week_transactions.count(),
                    'total_amount': float(week_transactions.aggregate(total=Sum('amount'))['total'] or 0)
                })
            except ImportError:
                weekly_data.append({
                    'week_start': str(current_week_start),
                    'week_end': str(week_end),
                    'transaction_count': 0,
                    'total_amount': 0
                })
            
            current_week_start += timedelta(days=7)
        
        # Calculate growth rates
        growth_rates = []
        for i in range(1, len(weekly_data)):
            prev_amount = weekly_data[i-1]['total_amount']
            curr_amount = weekly_data[i]['total_amount']
            
            if prev_amount > 0:
                growth_rate = ((curr_amount - prev_amount) / prev_amount) * 100
            else:
                growth_rate = 0
            
            growth_rates.append(growth_rate)
        
        return {
            'weekly_breakdown': weekly_data,
            'growth_rates': growth_rates,
            'average_growth_rate': sum(growth_rates) / len(growth_rates) if growth_rates else 0
        }


def generate_transaction_report(start_date=None, end_date=None, format='dict') -> Dict:
    """Generate comprehensive transaction report"""
    reporter = TransactionReporter(start_date, end_date)
    
    report = {
        'summary': reporter.get_transaction_summary(),
        'daily_breakdown': reporter.get_daily_transaction_breakdown(),
        'top_users': reporter.get_user_transaction_analysis(),
        'trends': reporter.get_transaction_trends(),
        'generated_at': timezone.now().isoformat()
    }
    
    if format == 'json':
        return json.dumps(report, default=str, indent=2)
    
    return report
